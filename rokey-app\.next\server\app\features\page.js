(()=>{var e={};e.id=6944,e.ids=[6944],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8636:(e,t,r)=>{Promise.resolve().then(r.bind(r,62030))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22204:(e,t,r)=>{Promise.resolve().then(r.bind(r,58488))},23703:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>c,tree:()=>u});var s=r(65239),o=r(48088),a=r(88170),n=r.n(a),i=r(30893),p={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>i[e]);r.d(t,p);let u={children:["",{children:["features",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58488)),"C:\\RoKey App\\rokey-app\\src\\app\\features\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\features\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/features/page",pathname:"/features",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58488:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>o});var s=r(12907);let o=(0,s.registerClientReference)(function(){throw Error("Attempted to call metadata() from the server but metadata is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\features\\page.tsx","metadata"),a=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\features\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\features\\page.tsx","default")},62030:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m You are attempting to export \"metadata\" from a component marked with \"use client\", which is disallowed. Either remove the export, or the \"use client\" directive. Read more: https://nextjs.org/docs/app/api-reference/directives/use-client\n  \x1b[31m│\x1b[0m\n\n    ╭─[\x1b[36;1;4mC:\\RoKey App\\rokey-app\\src\\app\\features\\page.tsx\x1b[0m:29:1]\n \x1b[2m26\x1b[0m │ import InstantLink from '@/components/ui/InstantLink';\n \x1b[2m27\x1b[0m │ import { Metadata } from 'next';\n \x1b[2m28\x1b[0m │ \n \x1b[2m29\x1b[0m │ export const metadata: Metadata = {\n    \xb7 \x1b[35;1m             ────────\x1b[0m\n \x1b[2m30\x1b[0m │   title: 'RouKey Features - Advanced AI Gateway with Multi-Role Orchestration | RouKey',\n \x1b[2m31\x1b[0m │   description: 'Discover RouKey\\'s unique features: Multi-Role Orchestration, Smart Cost Optimization, 300+ AI models, unlimited requests, document processing, and advanced analytics. The only AI gateway you\\'ll ever need.',\n \x1b[2m32\x1b[0m │   keywords: 'AI gateway features, multi-role orchestration, cost optimization, AI routing, unlimited API requests, document processing, AI analytics, smart routing',\n    ╰────\n")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(31658);let o=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,221,1658,7437],()=>r(23703));module.exports=s})();