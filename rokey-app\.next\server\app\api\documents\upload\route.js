(()=>{var e={};e.id=3084,e.ids=[1489,3084,8108],e.modules={2507:(e,t,n)=>{"use strict";n.d(t,{Q:()=>a,createSupabaseServerClientOnRequest:()=>i});var r=n(34386),s=n(44999);async function i(){let e=await (0,s.UL)();return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,n,r){try{e.set({name:t,value:n,...r})}catch(e){}},remove(t,n){try{e.set({name:t,value:"",...n})}catch(e){}}}})}function a(e){return(0,r.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,n){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88108:(e,t,n)=>{"use strict";n.d(t,{jinaEmbeddings:()=>s});class r{constructor(){if(this.currentKeyIndex=0,this.keyUsage=new Map,this.baseUrl="https://api.jina.ai/v1/embeddings",this.model="jina-embeddings-v3",this.apiKeys=[process.env.JINA_API_KEY,process.env.JINA_API_KEY_2,process.env.JINA_API_KEY_3,process.env.JINA_API_KEY_4,process.env.JINA_API_KEY_5,process.env.JINA_API_KEY_6,process.env.JINA_API_KEY_7,process.env.JINA_API_KEY_9,process.env.JINA_API_KEY_10].filter(Boolean),0===this.apiKeys.length)throw Error("No Jina API keys found in environment variables");this.apiKeys.forEach(e=>{this.keyUsage.set(e,{requests:0,tokens:0,lastUsed:new Date,errors:0})})}getNextKey(){let e=this.apiKeys[this.currentKeyIndex];return this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,e}getBestKey(){return this.getNextKey()}updateKeyUsage(e,t,n=!1){let r=this.keyUsage.get(e);r&&(r.requests++,r.tokens+=t,r.lastUsed=new Date,n&&(r.errors++,r.lastError=new Date))}async embedQuery(e){let t=this.apiKeys.length,n=null;for(let r=0;r<t;r++)try{let t=this.getBestKey(),n=await fetch(this.baseUrl,{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({model:this.model,input:[e],normalized:!0,embedding_type:"float"})});if(!n.ok){let e=await n.text();if(429===n.status){this.updateKeyUsage(t,0,!0);continue}throw Error(`HTTP ${n.status}: ${e}`)}let r=await n.json();if(!r.data||0===r.data.length)throw Error("No embedding data returned from Jina API");let s=r.data[0].embedding;return this.updateKeyUsage(t,r.usage?.total_tokens||e.length),s}catch(e){if(n=e,r===t-1)break}throw Error(`All Jina API keys failed. Last error: ${n?.message||"Unknown error"}`)}async embedDocuments(e){let t=[];for(let n=0;n<e.length;n++){let r=await this.embedQuery(e[n]);t.push(r),n<e.length-1&&await new Promise(e=>setTimeout(e,100))}return t}getUsageStats(){let e={};return this.apiKeys.forEach((t,n)=>{let r=this.keyUsage.get(t);r&&(e[`key_${n+1}`]={...r})}),e}getTotalCapacity(){return{totalKeys:this.apiKeys.length,estimatedRPM:500*this.apiKeys.length,estimatedTokensPerMonth:1e6*this.apiKeys.length}}}let s=new r},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},97597:(e,t,n)=>{"use strict";n.r(t),n.d(t,{patchFetch:()=>I,routeModule:()=>k,serverHooks:()=>_,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>x});var r={};n.r(r),n.d(r,{POST:()=>b});var s=n(96559),i=n(48088),a=n(37719),o=n(32190),l=n(2507),u=n(76947);n(88595);class c extends u.BaseDocumentTransformer{constructor(e){if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","document_transformers","text_splitters"]}),Object.defineProperty(this,"chunkSize",{enumerable:!0,configurable:!0,writable:!0,value:1e3}),Object.defineProperty(this,"chunkOverlap",{enumerable:!0,configurable:!0,writable:!0,value:200}),Object.defineProperty(this,"keepSeparator",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"lengthFunction",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.chunkSize=e?.chunkSize??this.chunkSize,this.chunkOverlap=e?.chunkOverlap??this.chunkOverlap,this.keepSeparator=e?.keepSeparator??this.keepSeparator,this.lengthFunction=e?.lengthFunction??(e=>e.length),this.chunkOverlap>=this.chunkSize)throw Error("Cannot have chunkOverlap >= chunkSize")}async transformDocuments(e,t={}){return this.splitDocuments(e,t)}splitOnSeparator(e,t){let n;if(t)if(this.keepSeparator){let r=t.replace(/[/\-\\^$*+?.()|[\]{}]/g,"\\$&");n=e.split(RegExp(`(?=${r})`))}else n=e.split(t);else n=e.split("");return n.filter(e=>""!==e)}async createDocuments(e,t=[],n={}){let r=t.length>0?t:[...Array(e.length)].map(()=>({})),{chunkHeader:s="",chunkOverlapHeader:i="(cont'd) ",appendChunkOverlapHeader:a=!1}=n,o=[];for(let t=0;t<e.length;t+=1){let n=e[t],l=1,c=null,p=-1;for(let e of(await this.splitText(n))){let h=s,d=n.indexOf(e,p+1);if(null===c)l+=this.numberOfNewLines(n,0,d);else{let e=p+await this.lengthFunction(c);e<d?l+=this.numberOfNewLines(n,e,d):e>d&&(l-=this.numberOfNewLines(n,d,e)),a&&(h+=i)}let f=this.numberOfNewLines(e),m=r[t].loc&&"object"==typeof r[t].loc?{...r[t].loc}:{};m.lines={from:l,to:l+f};let g={...r[t],loc:m};h+=e,o.push(new u.Document({pageContent:h,metadata:g})),l+=f,c=e,p=d}}return o}numberOfNewLines(e,t,n){return(e.slice(t,n).match(/\n/g)||[]).length}async splitDocuments(e,t={}){let n=e.filter(e=>void 0!==e.pageContent),r=n.map(e=>e.pageContent),s=n.map(e=>e.metadata);return this.createDocuments(r,s,t)}joinDocs(e,t){let n=e.join(t).trim();return""===n?null:n}async mergeSplits(e,t){let n=[],r=[],s=0;for(let i of e){let e=await this.lengthFunction(i);if(s+e+r.length*t.length>this.chunkSize&&(s>this.chunkSize&&console.warn(`Created a chunk of size ${s}, +
which is longer than the specified ${this.chunkSize}`),r.length>0)){let i=this.joinDocs(r,t);for(null!==i&&n.push(i);s>this.chunkOverlap||s+e+r.length*t.length>this.chunkSize&&s>0;)s-=await this.lengthFunction(r[0]),r.shift()}r.push(i),s+=e}let i=this.joinDocs(r,t);return null!==i&&n.push(i),n}}class p extends c{static lc_name(){return"RecursiveCharacterTextSplitter"}constructor(e){super(e),Object.defineProperty(this,"separators",{enumerable:!0,configurable:!0,writable:!0,value:["\n\n","\n"," ",""]}),this.separators=e?.separators??this.separators,this.keepSeparator=e?.keepSeparator??!0}async _splitText(e,t){let n,r=[],s=t[t.length-1];for(let r=0;r<t.length;r+=1){let i=t[r];if(""===i){s=i;break}if(e.includes(i)){s=i,n=t.slice(r+1);break}}let i=this.splitOnSeparator(e,s),a=[],o=this.keepSeparator?"":s;for(let e of i)if(await this.lengthFunction(e)<this.chunkSize)a.push(e);else{if(a.length){let e=await this.mergeSplits(a,o);r.push(...e),a=[]}if(n){let t=await this._splitText(e,n);r.push(...t)}else r.push(e)}if(a.length){let e=await this.mergeSplits(a,o);r.push(...e)}return r}async splitText(e){return this._splitText(e,this.separators)}static fromLanguage(e,t){return new p({...t,separators:p.getSeparatorsForLanguage(e)})}static getSeparatorsForLanguage(e){if("cpp"===e)return["\nclass ","\nvoid ","\nint ","\nfloat ","\ndouble ","\nif ","\nfor ","\nwhile ","\nswitch ","\ncase ","\n\n","\n"," ",""];if("go"===e)return["\nfunc ","\nvar ","\nconst ","\ntype ","\nif ","\nfor ","\nswitch ","\ncase ","\n\n","\n"," ",""];if("java"===e)return["\nclass ","\npublic ","\nprotected ","\nprivate ","\nstatic ","\nif ","\nfor ","\nwhile ","\nswitch ","\ncase ","\n\n","\n"," ",""];if("js"===e)return["\nfunction ","\nconst ","\nlet ","\nvar ","\nclass ","\nif ","\nfor ","\nwhile ","\nswitch ","\ncase ","\ndefault ","\n\n","\n"," ",""];if("php"===e)return["\nfunction ","\nclass ","\nif ","\nforeach ","\nwhile ","\ndo ","\nswitch ","\ncase ","\n\n","\n"," ",""];else if("proto"===e)return["\nmessage ","\nservice ","\nenum ","\noption ","\nimport ","\nsyntax ","\n\n","\n"," ",""];else if("python"===e)return["\nclass ","\ndef ","\n	def ","\n\n","\n"," ",""];else if("rst"===e)return["\n===\n","\n---\n","\n***\n","\n.. ","\n\n","\n"," ",""];else if("ruby"===e)return["\ndef ","\nclass ","\nif ","\nunless ","\nwhile ","\nfor ","\ndo ","\nbegin ","\nrescue ","\n\n","\n"," ",""];else if("rust"===e)return["\nfn ","\nconst ","\nlet ","\nif ","\nwhile ","\nfor ","\nloop ","\nmatch ","\nconst ","\n\n","\n"," ",""];else if("scala"===e)return["\nclass ","\nobject ","\ndef ","\nval ","\nvar ","\nif ","\nfor ","\nwhile ","\nmatch ","\ncase ","\n\n","\n"," ",""];else if("swift"===e)return["\nfunc ","\nclass ","\nstruct ","\nenum ","\nif ","\nfor ","\nwhile ","\ndo ","\nswitch ","\ncase ","\n\n","\n"," ",""];else if("markdown"===e)return["\n## ","\n### ","\n#### ","\n##### ","\n###### ","```\n\n","\n\n***\n\n","\n\n---\n\n","\n\n___\n\n","\n\n","\n"," ",""];else if("latex"===e)return["\n\\chapter{","\n\\section{","\n\\subsection{","\n\\subsubsection{","\n\\begin{enumerate}","\n\\begin{itemize}","\n\\begin{description}","\n\\begin{list}","\n\\begin{quote}","\n\\begin{quotation}","\n\\begin{verse}","\n\\begin{verbatim}","\n\\begin{align}","$$","$","\n\n","\n"," ",""];else if("html"===e)return["<body>","<div>","<p>","<br>","<li>","<h1>","<h2>","<h3>","<h4>","<h5>","<h6>","<span>","<table>","<tr>","<td>","<th>","<ul>","<ol>","<header>","<footer>","<nav>","<head>","<style>","<script>","<meta>","<title>"," ",""];else if("sol"===e)return["\npragma ","\nusing ","\ncontract ","\ninterface ","\nlibrary ","\nconstructor ","\ntype ","\nfunction ","\nevent ","\nmodifier ","\nerror ","\nstruct ","\nenum ","\nif ","\nfor ","\nwhile ","\ndo while ","\nassembly ","\n\n","\n"," ",""];else throw Error(`Language ${e} is not supported.`)}}var h=n(88108);let d=require("fs/promises");var f=n(33873);let m=require("os"),g=require("pdf-parse");var w=n.n(g);let y=new p({chunkSize:1500,chunkOverlap:300,separators:["\n\n","\n",". ","! ","? ","; ",", "," ",""]});async function b(e){try{let t=await (0,l.createSupabaseServerClientOnRequest)(),{data:{user:r},error:s}=await t.auth.getUser();if(s||!r)return o.NextResponse.json({error:"Unauthorized"},{status:401});let i=await e.formData(),a=i.get("file"),u=i.get("configId");if(!a||!u)return o.NextResponse.json({error:"File and configId are required"},{status:400});if(!["application/pdf","text/plain","text/markdown"].includes(a.type))return o.NextResponse.json({error:"Unsupported file type. Please upload PDF, TXT, or MD files."},{status:400});if(a.size>0xa00000)return o.NextResponse.json({error:"File size too large. Maximum size is 10MB."},{status:400});let c=await a.arrayBuffer(),p=Buffer.from(c),g=(0,f.join)((0,m.tmpdir)(),`upload_${Date.now()}_${a.name}`);await (0,d.writeFile)(g,p);let b="";try{switch(a.type){case"application/pdf":let e=console.warn;console.warn=t=>{"string"==typeof t&&t.includes("Ran out of space in font private use area")||e(t)};try{if((!(b=(await w()(p,{max:0})).text)||0===b.trim().length)&&(!(b=(await w()(p,{max:0})).text)||0===b.trim().length))throw Error("No text could be extracted from this PDF. The file may be image-based, password-protected, or corrupted.")}catch(e){throw Error(`Failed to process PDF: ${e.message||"Unknown error"}`)}finally{console.warn=e}break;case"application/vnd.openxmlformats-officedocument.wordprocessingml.document":throw Error("DOCX support temporarily disabled. Please use PDF, TXT, or MD files.");case"text/plain":case"text/markdown":b=await (0,d.readFile)(g,"utf-8");break;default:throw Error("Unsupported file type")}let s={pageContent:b,metadata:{source:a.name,type:a.type}},i=await y.splitDocuments([s]),{data:l,error:c}=await t.from("documents").insert({user_id:r.id,custom_api_config_id:u,filename:a.name,file_type:a.type,file_size:a.size,content:b,metadata:{chunks_count:i.length,processing_started_at:new Date().toISOString()},status:"processing"}).select().single();if(c)throw Error("Failed to store document metadata");try{let e=await h.jinaEmbeddings.embedQuery("test");if(1024!==e.length)throw Error(`Dimension mismatch: Database expects 1024 dimensions but Jina v3 produces ${e.length} dimensions`)}catch(e){throw Error(`Jina embedding generation failed: ${e.message}`)}let f=i.map(async(e,n)=>{try{let s=await h.jinaEmbeddings.embedQuery(e.pageContent),i={document_id:l.id,user_id:r.id,custom_api_config_id:u,content:e.pageContent,metadata:{...e.metadata,chunk_index:n,chunk_size:e.pageContent.length},embedding:s},{data:a,error:o}=await t.from("document_chunks").insert(i).select().single();if(o)throw o;return{success:!0,index:n,chunkId:a.id}}catch(e){return{success:!1,index:n,error:e.message||e}}}),m=await Promise.all(f),k=m.filter(e=>e.success).length,v=m.filter(e=>!e.success).length,x=m.filter(e=>!e.success),_=0===v?"completed":"failed",{error:I}=await t.from("documents").update({status:_,metadata:{...l.metadata,chunks_processed:k,chunks_failed:v,processing_completed_at:new Date().toISOString(),...v>0&&{failed_chunk_errors:x.slice(0,5)}},updated_at:new Date().toISOString()}).eq("id",l.id);await (0,d.unlink)(g);try{let{trainingDataCache:e}=await n.e(2842).then(n.bind(n,2842));e.invalidate(u)}catch(e){}return o.NextResponse.json({success:!0,document:{id:l.id,filename:a.name,status:_,chunks_processed:k,chunks_total:i.length}})}catch(e){try{await (0,d.unlink)(g)}catch(e){}throw e}}catch(e){return o.NextResponse.json({error:"Failed to process document",details:e.message},{status:500})}}let k=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/documents/upload/route",pathname:"/api/documents/upload",filename:"route",bundlePath:"app/api/documents/upload/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\documents\\upload\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:v,workUnitAsyncStorage:x,serverHooks:_}=k;function I(){return(0,a.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:x})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[4447,580,9398,3410,5697,5601],()=>n(97597));module.exports=r})();