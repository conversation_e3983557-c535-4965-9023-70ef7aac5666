"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4696],{21448:(t,e,i)=>{function n(t,e){-1===t.indexOf(e)&&t.push(e)}function s(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}function r([...t],e,i){let n=e<0?t.length+e:e;if(n>=0&&n<t.length){let n=i<0?t.length+i:i,[s]=t.splice(e,1);t.splice(n,0,s)}return t}i.d(e,{W9:()=>u,vY:()=>v,Kq:()=>n,bt:()=>P,ZZ:()=>D,tn:()=>W,yT:()=>V,qE:()=>a,am:()=>I,KI:()=>L,Sb:()=>R,V1:()=>l,DW:()=>X,h0:()=>q,iW:()=>h,Gv:()=>d,$X:()=>p,ph:()=>m,Xu:()=>T,Pe:()=>r,lQ:()=>c,Fs:()=>g,qB:()=>y,Ai:()=>s,fD:()=>b,fj:()=>w,$e:()=>o});let a=(t,e,i)=>i>e?e:i<t?t:i,o=()=>{},l=()=>{},u={},h=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t);function d(t){return"object"==typeof t&&null!==t}let p=t=>/^0[^.\s]+$/u.test(t);function m(t){let e;return()=>(void 0===e&&(e=t()),e)}let c=t=>t,f=(t,e)=>i=>e(t(i)),g=(...t)=>t.reduce(f),y=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n};class v{constructor(){this.subscriptions=[]}add(t){return n(this.subscriptions,t),()=>s(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let b=t=>1e3*t,T=t=>t/1e3;function w(t,e){return e?1e3/e*t:0}let M=(t,e,i)=>{let n=e-t;return((i-t)%n+n)%n+t},A=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function k(t,e,i,n){if(t===e&&i===n)return c;let s=e=>(function(t,e,i,n,s){let r,a,o=0;do(r=A(a=e+(i-e)/2,n,s)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:A(s(t),e,n)}let x=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,E=t=>e=>1-t(1-e),S=k(.33,1.53,.69,.99),F=E(S),D=x(F),P=t=>(t*=2)<1?.5*F(t):.5*(2-Math.pow(2,-10*(t-1))),$=t=>1-Math.sin(Math.acos(t)),V=E($),W=x($),O=k(.42,0,1,1),C=k(0,0,.58,1),I=k(.42,0,.58,1),q=t=>Array.isArray(t)&&"number"!=typeof t[0];function R(t,e){return q(t)?t[M(0,t.length,e)]:t}let X=t=>Array.isArray(t)&&"number"==typeof t[0],K={linear:c,easeIn:O,easeInOut:I,easeOut:C,circIn:$,circInOut:W,circOut:V,backIn:F,backInOut:D,backOut:S,anticipate:P},N=t=>"string"==typeof t,L=t=>{if(X(t)){l(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,s]=t;return k(e,i,n,s)}return N(t)?(l(void 0!==K[t],`Invalid easing type '${t}'`),K[t]):t}},43891:(t,e,i)=>{let n;i.d(e,{AT:()=>el,KN:()=>eS,b3:()=>eh,sb:()=>tW,hP:()=>t3,KG:()=>eO,MW:()=>eD,qU:()=>c,WG:()=>u,bt:()=>eW,f:()=>G,XG:()=>tc,ZC:()=>tx,zs:()=>tN,fs:()=>tk,tD:()=>e8,Gt:()=>l,uv:()=>h,PP:()=>d,Ju:()=>ex,Df:()=>ek,eK:()=>e$,rU:()=>ep,PT:()=>eN,j4:()=>g,WH:()=>et,$P:()=>er,SS:()=>e6,Mc:()=>eY,xZ:()=>eQ,h1:()=>e5,k2:()=>eI,k$:()=>H,OQ:()=>eC,Wh:()=>eM,rq:()=>O,$y:()=>em,c$:()=>eU,px:()=>C,Ib:()=>tY,KJ:()=>eP,Wp:()=>eR,oz:()=>tw,Qu:()=>a,kB:()=>m,pd:()=>e4,Us:()=>tZ,fu:()=>tz});var s=i(21448);let r=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],a={value:null,addProjectionMetrics:null};function o(t,e){let i=!1,n=!0,o={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,u=r.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,s=!1,r=!1,o=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function h(e){o.has(e)&&(d.schedule(e),t()),u++,e(l)}let d={schedule:(t,e=!1,r=!1)=>{let a=r&&s?i:n;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{if(l=t,s){r=!0;return}s=!0,[i,n]=[n,i],i.forEach(h),e&&a.value&&a.value.frameloop[e].push(u),u=0,i.clear(),s=!1,r&&(r=!1,d.process(t))}};return d}(l,e?i:void 0),t),{}),{setup:h,read:d,resolveKeyframes:p,preUpdate:m,update:c,preRender:f,render:g,postRender:y}=u,v=()=>{let r=s.W9.useManualTiming?o.timestamp:performance.now();i=!1,s.W9.useManualTiming||(o.delta=n?1e3/60:Math.max(Math.min(r-o.timestamp,40),1)),o.timestamp=r,o.isProcessing=!0,h.process(o),d.process(o),p.process(o),m.process(o),c.process(o),f.process(o),g.process(o),y.process(o),o.isProcessing=!1,i&&e&&(n=!1,t(v))},b=()=>{i=!0,n=!0,o.isProcessing||t(v)};return{schedule:r.reduce((t,e)=>{let n=u[e];return t[e]=(t,e=!1,s=!1)=>(i||b(),n.schedule(t,e,s)),t},{}),cancel:t=>{for(let e=0;e<r.length;e++)u[r[e]].cancel(t)},state:o,steps:u}}let{schedule:l,cancel:u,state:h,steps:d}=o("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:s.lQ,!0);function p(){n=void 0}let m={now:()=>(void 0===n&&m.set(h.isProcessing||s.W9.useManualTiming?h.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(p)}},c={layout:0,mainThread:0,waapi:0},f=t=>e=>"string"==typeof e&&e.startsWith(t),g=f("--"),y=f("var(--"),v=t=>!!y(t)&&b.test(t.split("/*")[0].trim()),b=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,T={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},w={...T,transform:t=>(0,s.qE)(0,1,t)},M={...T,default:1},A=t=>Math.round(1e5*t)/1e5,k=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,x=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,E=(t,e)=>i=>!!("string"==typeof i&&x.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),S=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,r,a,o]=n.match(k);return{[t]:parseFloat(s),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},F=t=>(0,s.qE)(0,255,t),D={...T,transform:t=>Math.round(F(t))},P={test:E("rgb","red"),parse:S("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+D.transform(t)+", "+D.transform(e)+", "+D.transform(i)+", "+A(w.transform(n))+")"},$={test:E("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:P.transform},V=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),W=V("deg"),O=V("%"),C=V("px"),I=V("vh"),q=V("vw"),R={...O,parse:t=>O.parse(t)/100,transform:t=>O.transform(100*t)},X={test:E("hsl","hue"),parse:S("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+O.transform(A(e))+", "+O.transform(A(i))+", "+A(w.transform(n))+")"},K={test:t=>P.test(t)||$.test(t)||X.test(t),parse:t=>P.test(t)?P.parse(t):X.test(t)?X.parse(t):$.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?P.transform(t):X.transform(t),getAnimatableNone:t=>{let e=K.parse(t);return e.alpha=0,K.transform(e)}},N=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,L="number",Y="color",B=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function Z(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],r=0,a=e.replace(B,t=>(K.test(t)?(n.color.push(r),s.push(Y),i.push(K.parse(t))):t.startsWith("var(")?(n.var.push(r),s.push("var"),i.push(t)):(n.number.push(r),s.push(L),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:n,types:s}}function z(t){return Z(t).values}function _(t){let{split:e,types:i}=Z(t),n=e.length;return t=>{let s="";for(let r=0;r<n;r++)if(s+=e[r],void 0!==t[r]){let e=i[r];e===L?s+=A(t[r]):e===Y?s+=K.transform(t[r]):s+=t[r]}return s}}let j=t=>"number"==typeof t?0:K.test(t)?K.getAnimatableNone(t):t,G={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(k)?.length||0)+(t.match(N)?.length||0)>0},parse:z,createTransformer:_,getAnimatableNone:function(t){let e=z(t);return _(t)(e.map(j))}};function U(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function Q(t,e){return i=>i>0?e:t}let H=(t,e,i)=>t+(e-t)*i,J=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},tt=[$,P,X],te=t=>tt.find(e=>e.test(t));function ti(t){let e=te(t);if((0,s.$e)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===X&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,a=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,o=2*i-n;s=U(o,n,t+1/3),r=U(o,n,t),a=U(o,n,t-1/3)}else s=r=a=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*a),alpha:n}}(i)),i}let tn=(t,e)=>{let i=ti(t),n=ti(e);if(!i||!n)return Q(t,e);let s={...i};return t=>(s.red=J(i.red,n.red,t),s.green=J(i.green,n.green,t),s.blue=J(i.blue,n.blue,t),s.alpha=H(i.alpha,n.alpha,t),P.transform(s))},ts=new Set(["none","hidden"]);function tr(t,e){return i=>H(t,e,i)}function ta(t){return"number"==typeof t?tr:"string"==typeof t?v(t)?Q:K.test(t)?tn:tu:Array.isArray(t)?to:"object"==typeof t?K.test(t)?tn:tl:Q}function to(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>ta(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function tl(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=ta(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let tu=(t,e)=>{let i=G.createTransformer(e),n=Z(t),r=Z(e);return n.indexes.var.length===r.indexes.var.length&&n.indexes.color.length===r.indexes.color.length&&n.indexes.number.length>=r.indexes.number.length?ts.has(t)&&!r.values.length||ts.has(e)&&!n.values.length?function(t,e){return ts.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,s.Fs)(to(function(t,e){let i=[],n={color:0,var:0,number:0};for(let s=0;s<e.values.length;s++){let r=e.types[s],a=t.indexes[r][n[r]],o=t.values[a]??0;i[s]=o,n[r]++}return i}(n,r),r.values),i):((0,s.$e)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),Q(t,e))};function th(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?H(t,e,i):ta(t)(t,e)}let td=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>l.update(e,t),stop:()=>u(e),now:()=>h.isProcessing?h.timestamp:m.now()}},tp=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=Math.round(1e4*t(e/(s-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`};function tm(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tc(t,e=100,i){let n=i({...t,keyframes:[0,e]}),r=Math.min(tm(n),2e4);return{type:"keyframes",ease:t=>n.next(r*t).value/e,duration:(0,s.Xu)(r)}}function tf(t,e,i){let n=Math.max(e-5,0);return(0,s.fj)(i-t(n),e-n)}let tg={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function ty(t,e){return t*Math.sqrt(1-e*e)}let tv=["duration","bounce"],tb=["stiffness","damping","mass"];function tT(t,e){return e.some(e=>void 0!==t[e])}function tw(t=tg.visualDuration,e=tg.bounce){let i,n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:r,restDelta:a}=n,o=n.keyframes[0],l=n.keyframes[n.keyframes.length-1],u={done:!1,value:o},{stiffness:h,damping:d,mass:p,duration:m,velocity:c,isResolvedFromDuration:f}=function(t){let e={velocity:tg.velocity,stiffness:tg.stiffness,damping:tg.damping,mass:tg.mass,isResolvedFromDuration:!1,...t};if(!tT(t,tb)&&tT(t,tv))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,r=2*(0,s.qE)(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:tg.mass,stiffness:n,damping:r}}else{let i=function({duration:t=tg.duration,bounce:e=tg.bounce,velocity:i=tg.velocity,mass:n=tg.mass}){let r,a;(0,s.$e)(t<=(0,s.fD)(tg.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=(0,s.qE)(tg.minDamping,tg.maxDamping,o),t=(0,s.qE)(tg.minDuration,tg.maxDuration,(0,s.Xu)(t)),o<1?(r=e=>{let n=e*o,s=n*t;return .001-(n-i)/ty(e,o)*Math.exp(-s)},a=e=>{let n=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-n),l=ty(Math.pow(e,2),o);return(n*i+i-s)*a*(-r(e)+.001>0?-1:1)/l}):(r=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),a=e=>t*t*(i-e)*Math.exp(-e*t));let l=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(r,a,5/t);if(t=(0,s.fD)(t),isNaN(l))return{stiffness:tg.stiffness,damping:tg.damping,duration:t};{let e=Math.pow(l,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:tg.mass}).isResolvedFromDuration=!0}return e}({...n,velocity:-(0,s.Xu)(n.velocity||0)}),g=c||0,y=d/(2*Math.sqrt(h*p)),v=l-o,b=(0,s.Xu)(Math.sqrt(h/p)),T=5>Math.abs(v);if(r||(r=T?tg.restSpeed.granular:tg.restSpeed.default),a||(a=T?tg.restDelta.granular:tg.restDelta.default),y<1){let t=ty(b,y);i=e=>l-Math.exp(-y*b*e)*((g+y*b*v)/t*Math.sin(t*e)+v*Math.cos(t*e))}else if(1===y)i=t=>l-Math.exp(-b*t)*(v+(g+b*v)*t);else{let t=b*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*b*e),n=Math.min(t*e,300);return l-i*((g+y*b*v)*Math.sinh(n)+t*v*Math.cosh(n))/t}}let w={calculatedDuration:f&&m||null,next:t=>{let e=i(t);if(f)u.done=t>=m;else{let n=0===t?g:0;y<1&&(n=0===t?(0,s.fD)(g):tf(i,t,e));let o=Math.abs(l-e)<=a;u.done=Math.abs(n)<=r&&o}return u.value=u.done?l:e,u},toString:()=>{let t=Math.min(tm(w),2e4),e=tp(e=>w.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return w}function tM({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:u=.5,restSpeed:h}){let d,p,m=t[0],c={done:!1,value:m},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,v=m+y,b=void 0===a?v:a(v);b!==v&&(y=b-m);let T=t=>-y*Math.exp(-t/n),w=t=>b+T(t),M=t=>{let e=T(t),i=w(t);c.done=Math.abs(e)<=u,c.value=c.done?b:i},A=t=>{f(c.value)&&(d=t,p=tw({keyframes:[c.value,g(c.value)],velocity:tf(w,t,c.value),damping:s,stiffness:r,restDelta:u,restSpeed:h}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(p||void 0!==d||(e=!0,M(t),A(t)),void 0!==d&&t>=d)?p.next(t-d):(e||M(t),c)}}}function tA(t,e,{clamp:i=!0,ease:n,mixer:r}={}){let a=t.length;if((0,s.V1)(a===e.length,"Both input and output ranges must be the same length"),1===a)return()=>e[0];if(2===a&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[a-1]&&(t=[...t].reverse(),e=[...e].reverse());let l=function(t,e,i){let n=[],r=i||s.W9.mix||th,a=t.length-1;for(let i=0;i<a;i++){let a=r(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||s.lQ:e;a=(0,s.Fs)(t,a)}n.push(a)}return n}(e,n,r),u=l.length,h=i=>{if(o&&i<t[0])return e[0];let n=0;if(u>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=(0,s.qB)(t[n],t[n+1],i);return l[n](r)};return i?e=>h((0,s.qE)(t[0],t[a-1],e)):h}function tk(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let r=(0,s.qB)(0,e,n);t.push(H(i,1,r))}}function tx(t){let e=[0];return tk(e,t.length-1),e}function tE({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let a=(0,s.h0)(n)?n.map(s.KI):(0,s.KI)(n),o={done:!1,value:e[0]},l=tA((r=i&&i.length===e.length?i:tx(e),r.map(e=>e*t)),e,{ease:Array.isArray(a)?a:e.map(()=>a||s.am).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(o.value=l(e),o.done=e>=t,o)}}tw.applyToOptions=t=>{let e=tc(t,100,tw);return t.ease=e.ease,t.duration=(0,s.fD)(e.duration),t.type="keyframes",t};let tS=t=>null!==t;function tF(t,{repeat:e,repeatType:i="loop"},n,s=1){let r=t.filter(tS),a=s<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==n?n:r[a]}let tD={decay:tM,inertia:tM,tween:tE,keyframes:tE,spring:tw};function tP(t){"string"==typeof t.type&&(t.type=tD[t.type])}class t${constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let tV=t=>t/100;class tW extends t${constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==m.now()&&this.tick(m.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},c.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;tP(t);let{type:e=tE,repeat:i=0,repeatDelay:n=0,repeatType:r,velocity:a=0}=t,{keyframes:o}=t,l=e||tE;l!==tE&&"number"!=typeof o[0]&&(this.mixKeyframes=(0,s.Fs)(tV,th(o[0],o[1])),o=[0,100]);let u=l({...t,keyframes:o});"mirror"===r&&(this.mirroredGenerator=l({...t,keyframes:[...o].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=tm(u));let{calculatedDuration:h}=u;this.calculatedDuration=h,this.resolvedDuration=h+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=u}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:r,mirroredGenerator:a,resolvedDuration:o,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:u=0,keyframes:h,repeat:d,repeatType:p,repeatDelay:m,type:c,onUpdate:f,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-u*(this.playbackSpeed>=0?1:-1),v=this.playbackSpeed>=0?y<0:y>n;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let b=this.currentTime,T=i;if(d){let t=Math.min(this.currentTime,n)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/o)):"mirror"===p&&(T=a)),b=(0,s.qE)(0,1,i)*o}let w=v?{done:!1,value:h[0]}:T.next(b);r&&(w.value=r(w.value));let{done:M}=w;v||null===l||(M=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&M);return A&&c!==tM&&(w.value=tF(h,this.options,g,this.speed)),f&&f(w.value),A&&this.finish(),w}then(t,e){return this.finished.then(t,e)}get duration(){return(0,s.Xu)(this.calculatedDuration)}get time(){return(0,s.Xu)(this.currentTime)}set time(t){t=(0,s.fD)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(m.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,s.Xu)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=td,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(m.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,c.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let tO=t=>180*t/Math.PI,tC=t=>tq(tO(Math.atan2(t[1],t[0]))),tI={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:tC,rotateZ:tC,skewX:t=>tO(Math.atan(t[1])),skewY:t=>tO(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},tq=t=>((t%=360)<0&&(t+=360),t),tR=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),tX=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),tK={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:tR,scaleY:tX,scale:t=>(tR(t)+tX(t))/2,rotateX:t=>tq(tO(Math.atan2(t[6],t[5]))),rotateY:t=>tq(tO(Math.atan2(-t[2],t[0]))),rotateZ:tC,rotate:tC,skewX:t=>tO(Math.atan(t[4])),skewY:t=>tO(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function tN(t){return+!!t.includes("scale")}function tL(t,e){let i,n;if(!t||"none"===t)return tN(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=tK,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=tI,n=e}if(!n)return tN(e);let r=i[e],a=n[1].split(",").map(tB);return"function"==typeof r?r(a):a[r]}let tY=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return tL(i,e)};function tB(t){return parseFloat(t.trim())}let tZ=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],tz=new Set(tZ),t_=t=>t===T||t===C,tj=new Set(["x","y","z"]),tG=tZ.filter(t=>!tj.has(t)),tU={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>tL(e,"x"),y:(t,{transform:e})=>tL(e,"y")};tU.translateX=tU.x,tU.translateY=tU.y;let tQ=new Set,tH=!1,tJ=!1,t0=!1;function t1(){if(tJ){let t=Array.from(tQ).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return tG.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}tJ=!1,tH=!1,tQ.forEach(t=>t.complete(t0)),tQ.clear()}function t2(){tQ.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(tJ=!0)})}class t3{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(tQ.add(this),tH||(tH=!0,l.read(t2),l.resolveKeyframes(t1))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),tQ.delete(this)}cancel(){"scheduled"===this.state&&(tQ.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let t5=t=>t.startsWith("--"),t4=(0,s.ph)(()=>void 0!==window.ScrollTimeline),t6={},t9=function(t,e){let i=(0,s.ph)(t);return()=>t6[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),t8=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,t7={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:t8([0,.65,.55,1]),circOut:t8([.55,0,1,.45]),backIn:t8([.31,.01,.66,-.59]),backOut:t8([.33,1.53,.69,.99])};function et(t){return"function"==typeof t&&"applyToOptions"in t}class ee extends t${constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:o=!1,finalKeyframe:l,onComplete:u}=t;this.isPseudoElement=!!r,this.allowFlatten=o,this.options=t,(0,s.V1)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let h=function({type:t,...e}){return et(t)&&t9()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:o=0,repeatType:l="loop",ease:u="easeOut",times:h}={},d){let p={[e]:i};h&&(p.offset=h);let m=function t(e,i){if(e)return"function"==typeof e?t9()?tp(e,i):"ease-out":(0,s.DW)(e)?t8(e):Array.isArray(e)?e.map(e=>t(e,i)||t7.easeOut):t7[e]}(u,r);Array.isArray(m)&&(p.easing=m),a.value&&c.waapi++;let f={delay:n,duration:r,easing:Array.isArray(m)?"linear":m,fill:"both",iterations:o+1,direction:"reverse"===l?"alternate":"normal"};d&&(f.pseudoElement=d);let g=t.animate(p,f);return a.value&&g.finished.finally(()=>{c.waapi--}),g}(e,i,n,h,r),!1===h.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=tF(n,this.options,l,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){t5(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}u?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,s.Xu)(Number(t))}get time(){return(0,s.Xu)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,s.fD)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&t4())?(this.animation.timeline=t,s.lQ):e(this)}}let ei={anticipate:s.bt,backInOut:s.ZZ,circInOut:s.tn};class en extends ee{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in ei&&(t.ease=ei[t.ease])}(t),tP(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...a}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new tW({...a,autoplay:!1}),l=(0,s.fD)(this.finishedTime??this.time);e.setWithVelocity(o.sample(l-10).value,o.sample(l).value,10),o.stop()}}let es=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(G.test(t)||"0"===t)&&!t.startsWith("url("));function er(t){return(0,s.Gv)(t)&&"offsetHeight"in t}let ea=new Set(["opacity","clipPath","filter","transform"]),eo=(0,s.ph)(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class el extends t${constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=m.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:o,motionValue:l,element:u,...h},p=u?.KeyframeResolver||t3;this.keyframeResolver=new p(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:r,type:a,velocity:o,delay:l,isHandoff:u,onUpdate:h}=i;this.resolvedAt=m.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let a=t[t.length-1],o=es(r,e),l=es(a,e);return(0,s.$e)(o===l,`You are trying to animate ${e} from "${r}" to "${a}". ${r} is not an animatable value - to enable this animation set ${r} to a value animatable to ${a} via the \`style\` property.`),!!o&&!!l&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||et(i))&&n)}(t,r,a,o)&&((s.W9.instantAnimations||!l)&&h?.(tF(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let d={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!u&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!er(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return eo()&&i&&ea.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}(d)?new en({...d,element:d.motionValue.owner.current}):new tW(d);p.finished.then(()=>this.notifyFinished()).catch(s.lQ),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),t0=!0,t2(),t1(),t0=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}class eu{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>t.finished))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t){let e=this.animations.map(e=>e.attachTimeline(t));return()=>{e.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get state(){return this.getAll("state")}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class eh extends eu{then(t,e){return this.finished.finally(t).then(()=>{})}}new WeakMap;let ed=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function ep(t,e){return t?.[e]??t?.default??t}let em=new Set(["width","height","top","left","right","bottom",...tZ]),ec=t=>e=>e.test(t),ef=[T,C,O,W,q,I,{test:t=>"auto"===t,parse:t=>t}],eg=t=>ef.find(ec(t)),ey=new Set(["brightness","contrast","saturate","opacity"]);function ev(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(k)||[];if(!n)return t;let s=i.replace(n,""),r=+!!ey.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let eb=/\b([a-z-]*)\(.*?\)/gu,eT={...G,getAnimatableNone:t=>{let e=t.match(eb);return e?e.map(ev).join(" "):t}},ew={...T,transform:Math.round},eM={borderWidth:C,borderTopWidth:C,borderRightWidth:C,borderBottomWidth:C,borderLeftWidth:C,borderRadius:C,radius:C,borderTopLeftRadius:C,borderTopRightRadius:C,borderBottomRightRadius:C,borderBottomLeftRadius:C,width:C,maxWidth:C,height:C,maxHeight:C,top:C,right:C,bottom:C,left:C,padding:C,paddingTop:C,paddingRight:C,paddingBottom:C,paddingLeft:C,margin:C,marginTop:C,marginRight:C,marginBottom:C,marginLeft:C,backgroundPositionX:C,backgroundPositionY:C,rotate:W,rotateX:W,rotateY:W,rotateZ:W,scale:M,scaleX:M,scaleY:M,scaleZ:M,skew:W,skewX:W,skewY:W,distance:C,translateX:C,translateY:C,translateZ:C,x:C,y:C,z:C,perspective:C,transformPerspective:C,opacity:w,originX:R,originY:R,originZ:C,zIndex:ew,fillOpacity:w,strokeOpacity:w,numOctaves:ew},eA={...eM,color:K,backgroundColor:K,outlineColor:K,fill:K,stroke:K,borderColor:K,borderTopColor:K,borderRightColor:K,borderBottomColor:K,borderLeftColor:K,filter:eT,WebkitFilter:eT},ek=t=>eA[t];function ex(t,e){let i=ek(t);return i!==eT&&(i=G),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let eE=new Set(["auto","none","0"]);class eS extends t3{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&v(n=n.trim())){let r=function t(e,i,n=1){(0,s.V1)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[r,a]=function(t){let e=ed.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!r)return;let o=window.getComputedStyle(i).getPropertyValue(r);if(o){let t=o.trim();return(0,s.iW)(t)?parseFloat(t):t}return v(a)?t(a,i,n+1):a}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!em.has(i)||2!==t.length)return;let[n,r]=t,a=eg(n),o=eg(r);if(a!==o)if(t_(a)&&t_(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else tU[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||(0,s.$X)(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!eE.has(e)&&Z(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=ex(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tU[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=tU[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}function eF(t){return!!("function"==typeof t&&supportsLinearEasing()||!t||"string"==typeof t&&(t in supportedWaapiEasing||supportsLinearEasing())||isBezierDefinition(t)||Array.isArray(t)&&t.every(eF))}let eD=new Set(["opacity","clipPath","filter","transform"]);function eP(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let n=document;e&&(n=e.current);let s=i?.[t]??n.querySelectorAll(t);return s?Array.from(s):[]}return Array.from(t)}let e$=(t,e)=>e&&"number"==typeof t?e.transform(t):t,eV=t=>!isNaN(parseFloat(t)),eW={current:void 0};class eO{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=m.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=m.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=eV(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new s.vY);let i=this.events[t].add(e);return"change"===t?()=>{i(),l.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return eW.current&&eW.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=m.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,s.fj)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function eC(t,e){return new eO(t,e)}C.transform;let{schedule:eI}=o(queueMicrotask,!1),eq={x:!1,y:!1};function eR(t){if("x"===t||"y"===t)if(eq[t])return null;else return eq[t]=!0,()=>{eq[t]=!1};return eq.x||eq.y?null:(eq.x=eq.y=!0,()=>{eq.x=eq.y=!1})}function eX(t,e){let i=eP(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function eK(t){return!("touch"===t.pointerType||eq.x||eq.y)}function eN(t,e,i={}){let[n,s,r]=eX(t,i),a=t=>{if(!eK(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{eK(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}let eL=(t,e)=>!!e&&(t===e||eL(t,e.parentElement)),eY=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,eB=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),eZ=new WeakSet;function ez(t){return e=>{"Enter"===e.key&&t(e)}}function e_(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ej=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=ez(()=>{if(eZ.has(i))return;e_(i,"down");let t=ez(()=>{e_(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>e_(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function eG(t){return eY(t)&&!(eq.x||eq.y)}function eU(t,e,i={}){let[n,s,r]=eX(t,i),a=t=>{let n=t.currentTarget;if(!eG(t))return;eZ.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),eZ.has(n)&&eZ.delete(n),eG(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||eL(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),er(t))&&(t.addEventListener("focus",t=>ej(t,s)),eB.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}function eQ(t){return(0,s.Gv)(t)&&"ownerSVGElement"in t}let eH=new WeakMap;function eJ({target:t,borderBoxSize:e}){eH.get(t)?.forEach(i=>{i(t,{get width(){return(null)(t,e)},get height(){return(null)(t,e)}})})}function e0(){let{value:t}=statsBuffer;if(null===t)return void cancelFrame(e0);t.frameloop.rate.push(frameData.delta),t.animations.mainThread.push(activeAnimations.mainThread),t.animations.waapi.push(activeAnimations.waapi),t.animations.layout.push(activeAnimations.layout)}function e1(t){return t.reduce((t,e)=>t+e,0)/t.length}function e2(t,e=e1){return 0===t.length?{min:0,max:0,avg:0}:{min:Math.min(...t),max:Math.max(...t),avg:e(t)}}let e3=t=>Math.round(1e3/t);function e5(t){return eQ(t)&&"svg"===t.tagName}function e4(...t){let e=!Array.isArray(t[0]),i=e?0:-1,n=t[0+i],s=t[1+i],r=tA(s,t[2+i],t[3+i]);return e?r(n):r}let e6=t=>!!(t&&t.getVelocity),e9=[...ef,K,G],e8=t=>e9.find(ec(t)),e7=null,it=null;r.reduce((t,e)=>(t[e]=t=>u(t),t),{})}}]);