(()=>{var e={};e.id=7220,e.ids=[7220],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5152:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError:   \x1b[31m\xd7\x1b[0m You are attempting to export \"metadata\" from a component marked with \"use client\", which is disallowed. Either remove the export, or the \"use client\" directive. Read more: https://nextjs.org/docs/app/api-reference/directives/use-client\n  \x1b[31m│\x1b[0m\n\n    ╭─[\x1b[36;1;4mC:\\RoKey App\\rokey-app\\src\\app\\about\\page.tsx\x1b[0m:15:1]\n \x1b[2m12\x1b[0m │ import Footer from '@/components/landing/Footer';\n \x1b[2m13\x1b[0m │ import { Metadata } from 'next';\n \x1b[2m14\x1b[0m │ \n \x1b[2m15\x1b[0m │ export const metadata: Metadata = {\n    \xb7 \x1b[35;1m             ────────\x1b[0m\n \x1b[2m16\x1b[0m │   title: 'About RouKey - The Solo-Built AI Gateway Revolution | RouKey',\n \x1b[2m17\x1b[0m │   description: 'Meet Okoro David Chukwunyerem, the solo developer who built RouKey from frustration with expensive AI routing. Discover how one developer\\'s late-night coding sessions created the ultimate AI gateway with unlimited requests to 300+ models.',\n \x1b[2m18\x1b[0m │   keywords: 'RouKey founder, AI gateway creator, solo developer, AI routing platform, unlimited AI requests, multi-model AI, cost-effective AI solutions',\n    ╰────\n")},10782:(e,t,r)=>{Promise.resolve().then(r.bind(r,5152))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15510:(e,t,r)=>{Promise.resolve().then(r.bind(r,28770))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},28770:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>s});var o=r(12907);let s=(0,o.registerClientReference)(function(){throw Error("Attempted to call metadata() from the server but metadata is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\about\\page.tsx","metadata"),a=(0,o.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\about\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\about\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30423:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>l,pages:()=>d,routeModule:()=>c,tree:()=>u});var o=r(65239),s=r(48088),a=r(88170),i=r.n(a),n=r(30893),p={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>n[e]);r.d(t,p);let u={children:["",{children:["about",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,28770)),"C:\\RoKey App\\rokey-app\\src\\app\\about\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\RoKey App\\rokey-app\\src\\app\\about\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/about/page",pathname:"/about",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[4447,221,1658,7437],()=>r(30423));module.exports=o})();