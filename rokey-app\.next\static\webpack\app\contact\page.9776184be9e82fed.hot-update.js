"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./src/app/contact/page.tsx":
/*!**********************************!*\
  !*** ./src/app/contact/page.tsx ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EnvelopeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClockIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,CheckCircleIcon,ClockIcon,EnvelopeIcon,ExclamationTriangleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _emailjs_browser__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @emailjs/browser */ \"(app-pages-browser)/./node_modules/@emailjs/browser/es/index.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ContactPage() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        company: '',\n        subject: '',\n        message: ''\n    });\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [submitStatus, setSubmitStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('idle');\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        setIsSubmitting(true);\n        setSubmitStatus('idle');\n        try {\n            // Initialize EmailJS with your public key\n            _emailjs_browser__WEBPACK_IMPORTED_MODULE_2__[\"default\"].init(\"lm7-ATth2Cql60KIN\" || 0);\n            // Send email using EmailJS\n            await _emailjs_browser__WEBPACK_IMPORTED_MODULE_2__[\"default\"].send(\"service_2xtn7iv\" || 0, \"template_pg7e1af\" || 0, {\n                from_name: formData.name,\n                from_email: formData.email,\n                company: formData.company,\n                subject: formData.subject,\n                message: formData.message,\n                to_email: '<EMAIL>'\n            });\n            setSubmitStatus('success');\n            setFormData({\n                name: '',\n                email: '',\n                company: '',\n                subject: '',\n                message: ''\n            });\n        } catch (error) {\n            console.error('Error sending email:', error);\n            setSubmitStatus('error');\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const contactInfo = [\n        {\n            icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            title: 'Email Us',\n            details: [\n                '<EMAIL>',\n                '<EMAIL>'\n            ],\n            description: 'Send us an email and we\\'ll respond within 24 hours'\n        },\n        {\n            icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: 'Live Chat',\n            details: [\n                'Available 24/7'\n            ],\n            description: 'Chat with our team for immediate assistance'\n        },\n        {\n            icon: _barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: 'Response Time',\n            details: [\n                '< 24 hours'\n            ],\n            description: 'We pride ourselves on quick response times'\n        }\n    ];\n    const subjects = [\n        'General Inquiry',\n        'Technical Support',\n        'Sales Question',\n        'Partnership Opportunity',\n        'Feature Request',\n        'Bug Report',\n        'Billing Question',\n        'Other'\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-gray-50 to-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.h1, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"text-4xl md:text-5xl font-bold text-black mb-6\",\n                                children: [\n                                    \"Get in \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-[#ff6b35]\",\n                                        children: \"Touch\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                children: \"Have questions about RouKey? Want to discuss enterprise solutions? We're here to help you optimize your AI infrastructure.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 119,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: -20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            className: \"bg-white border border-gray-200 rounded-xl p-8 shadow-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-bold text-black mb-6\",\n                                    children: \"Send us a Message\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this),\n                                submitStatus === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 bg-green-50 border border-green-200 rounded-lg flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-5 w-5 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-800\",\n                                            children: \"Message sent successfully! We'll get back to you soon.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, this),\n                                submitStatus === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_CheckCircleIcon_ClockIcon_EnvelopeIcon_ExclamationTriangleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-red-800\",\n                                            children: \"Failed to send message. Please try again or email us directly.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSubmit,\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"name\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Name *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            id: \"name\",\n                                                            name: \"name\",\n                                                            required: true,\n                                                            value: formData.name,\n                                                            onChange: handleInputChange,\n                                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                                            placeholder: \"Your full name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"email\",\n                                                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                            children: \"Email *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 183,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"email\",\n                                                            id: \"email\",\n                                                            name: \"email\",\n                                                            required: true,\n                                                            value: formData.email,\n                                                            onChange: handleInputChange,\n                                                            className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                                            placeholder: \"<EMAIL>\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"company\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Company\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"company\",\n                                                    name: \"company\",\n                                                    value: formData.company,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                                    placeholder: \"Your company name\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"subject\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Subject *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"subject\",\n                                                    name: \"subject\",\n                                                    required: true,\n                                                    value: formData.subject,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select a subject\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 226,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        subjects.map((subject)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: subject,\n                                                                children: subject\n                                                            }, subject, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 218,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"message\",\n                                                    className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                    children: \"Message *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"message\",\n                                                    name: \"message\",\n                                                    required: true,\n                                                    rows: 6,\n                                                    value: formData.message,\n                                                    onChange: handleInputChange,\n                                                    className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent\",\n                                                    placeholder: \"Tell us how we can help you...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            disabled: isSubmitting,\n                                            className: \"w-full bg-[#ff6b35] text-white font-semibold py-3 px-6 rounded-lg hover:bg-[#f7931e] transition-colors disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            children: isSubmitting ? 'Sending...' : 'Send Message'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                x: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                x: 0\n                            },\n                            transition: {\n                                delay: 0.1\n                            },\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold text-black mb-6\",\n                                            children: \"Contact Information\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: contactInfo.map((info, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-[#ff6b35]/10 rounded-lg\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(info.icon, {\n                                                                className: \"h-6 w-6 text-[#ff6b35]\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 271,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-semibold text-black mb-1\",\n                                                                    children: info.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                                    lineNumber: 275,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                info.details.map((detail, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-700 font-medium\",\n                                                                        children: detail\n                                                                    }, idx, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                                        lineNumber: 277,\n                                                                        columnNumber: 25\n                                                                    }, this)),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 text-sm mt-1\",\n                                                                    children: info.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 274,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gray-50 rounded-xl p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-black mb-4\",\n                                            children: \"Frequently Asked Questions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-1\",\n                                                            children: \"How quickly can I get started?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: \"You can start using RouKey in under 5 minutes. Just sign up, add your API keys, and start routing!\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-1\",\n                                                            children: \"Do you offer enterprise support?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 295,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: \"Yes! Our Enterprise plan includes dedicated support, custom integrations, and SLA guarantees.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 296,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-1\",\n                                                            children: \"Can I switch routing strategies anytime?\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600 text-sm\",\n                                                            children: \"Absolutely! You can change routing strategies instantly without any downtime or configuration changes.\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                            lineNumber: 300,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                                    lineNumber: 298,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 rounded-xl p-6 border border-[#ff6b35]/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold text-black mb-4\",\n                                            children: \"Meet the Team\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-4\",\n                                            children: \"RouKey is built by a passionate team of AI engineers and developers who understand the challenges of production AI systems.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-sm\",\n                                            children: \"Founded by David Chukwunyerem, RouKey is designed to solve real-world AI routing challenges with innovative solutions.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\contact\\\\page.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactPage, \"c8bcck96/Y9F5NyMpg2t+MIgTHo=\");\n_c = ContactPage;\nvar _c;\n$RefreshReg$(_c, \"ContactPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/contact/page.tsx\n"));

/***/ })

});