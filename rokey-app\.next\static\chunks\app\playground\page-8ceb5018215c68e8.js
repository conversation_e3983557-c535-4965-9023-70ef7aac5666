(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3882],{306:(e,t,a)=>{"use strict";a.d(t,{vQ:()=>r.A,QG:()=>s,BZ:()=>i.A});var r=a(30192),n=a(12115);let s=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 12h14"}))});var i=a(86474)},2299:(e,t,a)=>{"use strict";a.d(t,{EF:()=>r.A,DQ:()=>n.A,C1:()=>s.A,Pp:()=>o,DP:()=>l.A,nr:()=>c,Y3:()=>d,XL:()=>u.A,$p:()=>h,R2:()=>m.A,P:()=>x,Zu:()=>g.A,BZ:()=>p.A,Gg:()=>f.A,K6:()=>v.A});var r=a(5279),n=a(64274),s=a(6865),i=a(12115);let o=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))});var l=a(5246);let c=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"}))}),d=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z"}))});var u=a(48666);let h=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});var m=a(61316);let x=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.59 14.37a6 6 0 0 1-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 0 0 6.16-12.12A14.98 14.98 0 0 0 9.631 8.41m5.96 5.96a14.926 14.926 0 0 1-5.841 2.58m-.119-8.54a6 6 0 0 0-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 0 0-2.58 5.84m2.699 2.7c-.103.021-.207.041-.311.06a15.09 15.09 0 0 1-2.448-2.448 14.9 14.9 0 0 1 .06-.312m-2.24 2.39a4.493 4.493 0 0 0-1.757 4.306 4.493 4.493 0 0 0 4.306-1.758M16.5 9a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0Z"}))});var g=a(8246),p=a(86474),f=a(27305),v=a(64219)},11485:(e,t,a)=>{"use strict";a.d(t,{B:()=>n.A,v:()=>r.A});var r=a(30192),n=a(86474)},22261:(e,t,a)=>{"use strict";a.d(t,{G:()=>i,c:()=>o});var r=a(95155),n=a(12115);let s=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[a,i]=(0,n.useState)(!0),[o,l]=(0,n.useState)(!1),[c,d]=(0,n.useState)(!1);return(0,r.jsx)(s.Provider,{value:{isCollapsed:a,isHovered:o,isHoverDisabled:c,toggleSidebar:()=>i(!a),collapseSidebar:()=>i(!0),expandSidebar:()=>i(!1),setHovered:e=>{c||l(e)},setHoverDisabled:e=>{d(e),e&&l(!1)}},children:t})}function o(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},24766:(e,t,a)=>{Promise.resolve().then(a.bind(a,79021))},57514:(e,t,a)=>{"use strict";a.d(t,{D:()=>n.A,E:()=>r.A});var r=a(5279),n=a(63418)},60875:(e,t,a)=>{"use strict";a.d(t,{BZ:()=>i.A,C1:()=>n.A,YE:()=>s.A,fl:()=>r.A});var r=a(89416),n=a(6865),s=a(58397),i=a(86474)},64134:(e,t,a)=>{"use strict";a.d(t,{g:()=>n});var r=a(12115);let n=r.forwardRef(function(e,t){let{title:a,titleId:n,...s}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),a?r.createElement("title",{id:n},a):null,r.createElement("path",{fillRule:"evenodd",d:"M5.25 2.25a3 3 0 0 0-3 3v4.318a3 3 0 0 0 .879 2.121l9.58 9.581c.92.92 2.39 1.186 3.548.428a18.849 18.849 0 0 0 5.441-5.44c.758-1.16.492-2.629-.428-3.548l-9.58-9.581a3 3 0 0 0-2.122-.879H5.25ZM6.375 7.5a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z",clipRule:"evenodd"}))})},76032:(e,t,a)=>{"use strict";a.d(t,{S:()=>r.A,X:()=>s});var r=a(29337),n=a(12115);let s=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M8.25 7.5V6.108c0-1.135.845-2.098 1.976-2.192.373-.03.748-.057 1.123-.08M15.75 18H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08M15.75 18.75v-1.875a3.375 3.375 0 0 0-3.375-3.375h-1.5a1.125 1.125 0 0 1-1.125-1.125v-1.5A3.375 3.375 0 0 0 6.375 7.5H5.25m11.9-3.664A2.251 2.251 0 0 0 15 2.25h-1.5a2.251 2.251 0 0 0-2.15 1.586m5.8 0c.065.21.1.433.1.664v.75h-6V4.5c0-.231.035-.454.1-.664M6.75 7.5H4.875c-.621 0-1.125.504-1.125 1.125v12c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V16.5a9 9 0 0 0-9-9Z"}))})},79021:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>j});var r=a(95155),n=a(12115);let s=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",clipRule:"evenodd"}))}),i=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{d:"M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"}))}),o=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M18.97 3.659a2.25 2.25 0 0 0-3.182 0l-10.94 10.94a3.75 3.75 0 1 0 5.304 5.303l7.693-7.693a.75.75 0 0 1 1.06 1.06l-7.693 7.693a5.25 5.25 0 1 1-7.424-7.424l10.939-10.94a3.75 3.75 0 1 1 5.303 5.304L9.097 18.835l-.008.008-.007.007-.002.002-.003.002A2.25 2.25 0 0 1 5.91 15.66l7.81-7.81a.75.75 0 0 1 1.061 1.06l-7.81 7.81a.75.75 0 0 0 1.054 1.068L18.97 6.84a2.25 2.25 0 0 0 0-3.182Z",clipRule:"evenodd"}))}),l=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z",clipRule:"evenodd"}))}),c=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))}),d=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"}))});var u=a(79112),h=a(95803),m=a(8413),x=a(43456),g=a(16910),p=a(4654),f=a(22261),v=a(24403),w=a(41e3),y=a(83298);a(96121);let b=n.memo(e=>{let{chat:t,currentConversation:a,onLoadChat:n,onDeleteChat:s}=e,i=(null==a?void 0:a.id)===t.id;return(0,r.jsxs)("div",{className:"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 ".concat(i?"bg-orange-50 border border-orange-200":""),children:[(0,r.jsx)("button",{onClick:()=>n(t),className:"w-full text-left",children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate mb-1",children:t.title}),t.last_message_preview&&(0,r.jsx)("p",{className:"text-xs text-gray-500 line-clamp-2 mb-2",children:t.last_message_preview}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[(0,r.jsxs)("span",{children:[t.message_count," messages"]}),(0,r.jsx)("span",{children:new Date(t.updated_at).toLocaleDateString()})]})]})})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),s(t.id)},className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200",title:"Delete conversation",children:(0,r.jsx)(l,{className:"w-4 h-4"})})]})});function j(){var e,t,a;let{isCollapsed:l,isHovered:j,setHoverDisabled:N}=(0,f.c)(),{user:k}=(0,y.R)(),S=!l||j?"256px":"64px",E=(null==k||null==(e=k.user_metadata)?void 0:e.first_name)||(null==k||null==(a=k.user_metadata)||null==(t=a.full_name)?void 0:t.split(" ")[0])||"",[C,T]=(0,n.useState)([]),[A,_]=(0,n.useState)(""),[L,O]=(0,n.useState)(!0);(0,n.useEffect)(()=>{A&&fetch("/api/keys?custom_config_id=".concat(A)).then(e=>e.json()).catch(e=>console.log("Background key prefetch failed:",e))},[A]);let[M,H]=(0,n.useState)(""),[R,I]=(0,n.useState)([]),[D,P]=(0,n.useState)(!1),[B,z]=(0,n.useState)(null),[W,F]=(0,n.useState)(!0),[Z,V]=(0,n.useState)(!1),[Y,U]=(0,n.useState)([]),[J,q]=(0,n.useState)([]),K=(0,n.useRef)(null),X=(0,n.useRef)(null),Q=(0,n.useRef)(null),[G,$]=(0,n.useState)(!1),[ee,et]=(0,n.useState)(null),[ea,er]=(0,n.useState)(null),[en,es]=(0,n.useState)(""),[ei,eo]=(0,n.useState)(!1),[el,ec]=(0,n.useState)(null),[ed,eu]=(0,n.useState)(!1),[eh,em]=(0,n.useState)(!1),[ex,eg]=(0,n.useState)(!1),[ep,ef]=(0,n.useState)(!1),[ev,ew]=(0,n.useState)(!1);(0,n.useEffect)(()=>{N(ex&&!ep)},[ex,ep,N]),(0,n.useEffect)(()=>{},[ed,el,ex,ep]);let ey=(0,w.w6)({enableAutoProgression:!0,onStageChange:(e,t)=>{}}),[eb,ej]=(0,n.useState)(""),eN=(e,t)=>{let a="";if(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**"))a="Multi-Role AI Orchestration Started";else if(e.includes("\uD83D\uDCCB **Orchestration Plan:**"))a="Planning specialist assignments";else if(e.includes("\uD83E\uDD16 **Moderator:**"))a="Moderator coordinating specialists";else if(e.includes("Specialist:")&&e.includes("Working...")){let t=e.match(/(\w+)\s+Specialist:/);a=t?"".concat(t[1]," Specialist working"):"Specialist working on your request"}else e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")?a="Synthesizing specialist responses":e.includes("Analyzing and processing")&&(a="Analyzing and processing with specialized expertise");a&&a!==eb&&(ej(a),t.updateOrchestrationStatus(a))},ek=async()=>{if(A&&ee){P(!0),ej("Continuing synthesis automatically..."),ey.startProcessing();try{let a={id:Date.now().toString()+"-continue",role:"user",content:[{type:"text",text:"continue"}]};I(e=>[...e,a]),await eW(ee.id,a);let r={custom_api_config_id:A,messages:[...R.map(e=>({role:e.role,content:1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content})),{role:"user",content:"continue"}],stream:W,...(null==k?void 0:k.id)&&{_internal_user_id:k.id}},n=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(r),cache:"no-store"});if(n.ok){let a,r=await n.text();try{a=JSON.parse(r)}catch(e){a=null}if((null==a?void 0:a.error)==="synthesis_complete"){I(e=>e.slice(0,-1)),P(!1),ej(""),ey.markComplete(),H("continue"),setTimeout(()=>{eQ()},100);return}let s=new Response(r,{status:n.status,statusText:n.statusText,headers:n.headers});if(W&&s.body){let a=s.body.getReader(),r=new TextDecoder,n=Date.now().toString()+"-assistant-continue",i={id:n,role:"assistant",content:[{type:"text",text:""}]};I(e=>[...e,i]);let o="",l=!1,c=null,d=s.headers.get("X-Synthesis-Progress"),u=s.headers.get("X-Synthesis-Complete"),h=null!==d;for(h?(ey.markStreaming(),ej("")):(ey.markOrchestrationStarted(),ej("Continuing synthesis..."),c=setTimeout(()=>{l||(ey.markStreaming(),ej(""))},800));;){let{done:s,value:d}=await a.read();if(s)break;for(let a of r.decode(d,{stream:!0}).split("\n"))if(a.startsWith("data: ")){let r=a.substring(6);if("[DONE]"===r.trim())break;try{var e,t;let a=JSON.parse(r);if(a.choices&&(null==(t=a.choices[0])||null==(e=t.delta)?void 0:e.content)){let e=a.choices[0].delta.content;o+=e,!h&&!l&&(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||e.includes("\uD83D\uDCCB **Orchestration Plan:**")||e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||e.includes("\uD83E\uDD16 **Moderator:**")||e.includes("Specialist:"))?(l=!0,c&&(clearTimeout(c),c=null),eN(e,ey)):!h&&l&&eN(e,ey);let t=i.content[0];t.text=o,I(e=>e.map(e=>e.id===n?{...e,content:[t]}:e))}}catch(e){}}}if(c&&clearTimeout(c),o){let e={...i,content:[{type:"text",text:o}]};h&&"true"!==u&&o.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")?(await eW(ee.id,e),setTimeout(()=>{ek()},1e3)):await eW(ee.id,e)}}}else throw Error("Auto-continuation failed: ".concat(n.status))}catch(t){let e={id:Date.now().toString()+"-error-continue",role:"error",content:[{type:"text",text:"Auto-continuation failed: ".concat(t instanceof Error?t.message:"Unknown error")}]};I(t=>[...t,e])}finally{P(!1),ej(""),ey.markComplete()}}},{chatHistory:eS,isLoading:eE,isStale:eC,error:eT,refetch:eA,prefetch:e_,invalidateCache:eL}=(0,v.mx)({configId:A,enablePrefetch:!0,cacheTimeout:3e5,staleTimeout:3e4}),{prefetchChatHistory:eO}=(0,v.l2)();(0,n.useEffect)(()=>{(async()=>{try{L&&await new Promise(e=>setTimeout(e,50));let e=await fetch("/api/custom-configs");if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to fetch configurations")}let t=await e.json();T(t),t.length>0&&_(t[0].id),O(!1)}catch(e){z("Failed to load configurations: ".concat(e.message)),T([]),O(!1)}})()},[L]);let eM=e=>new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)}),eH=async e=>{let t=Array.from(e.target.files||[]);if(0===t.length)return;let a=Y.length,r=t.slice(0,10-a);r.length<t.length&&z("You can only upload up to 10 images. ".concat(t.length-r.length," images were not added."));try{let e=[];for(let t of r){let a=await eM(t);e.push(a)}U(e=>[...e,...r]),q(t=>[...t,...e])}catch(e){z("Failed to process one or more images. Please try again.")}K.current&&(K.current.value="")},eR=e=>{void 0!==e?(U(t=>t.filter((t,a)=>a!==e)),q(t=>t.filter((t,a)=>a!==e))):(U([]),q([])),K.current&&(K.current.value="")},eI=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];Q.current&&Q.current.scrollTo({top:Q.current.scrollHeight,behavior:e?"smooth":"auto"})};(0,n.useEffect)(()=>{R.length>0&&requestAnimationFrame(()=>{eI()})},[R.length]),(0,n.useEffect)(()=>{D&&R.length>0&&requestAnimationFrame(()=>{eI()})},[R,D]),(0,n.useEffect)(()=>{if(D&&R.length>0){let e=R[R.length-1];e&&"assistant"===e.role&&requestAnimationFrame(()=>{eI()})}},[R,D]),(0,n.useEffect)(()=>{let e=setTimeout(()=>{R.length>0&&requestAnimationFrame(()=>{if(Q.current){let e=Q.current;e.scrollHeight-e.scrollTop-e.clientHeight<100&&eI()}})},200);return()=>clearTimeout(e)},[l,j,G,R.length]),(0,n.useEffect)(()=>{if(A&&C.length>0){let e=C.filter(e=>e.id!==A).slice(0,3),t=setTimeout(()=>{e.forEach(e=>{eO(e.id)})},2e3);return()=>clearTimeout(t)}},[A,C,eO]);let eD=async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t||eo(!0);try{let a=t?R.length:0,r=Date.now(),n=await fetch("/api/chat/messages?conversation_id=".concat(e.id,"&limit=").concat(50,"&offset=").concat(a,"&latest=").concat(!t,"&_cb=").concat(r),{cache:"no-store",headers:{"Cache-Control":"no-cache"}});if(!n.ok)throw Error("Failed to load conversation messages");let s=(await n.json()).map(e=>({id:e.id,role:e.role,content:e.content.map(e=>{var t;return"text"===e.type&&e.text?{type:"text",text:e.text}:"image_url"===e.type&&(null==(t=e.image_url)?void 0:t.url)?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:""}})}));t?I(e=>[...s,...e]):(I(s),ee&&ee.id===e.id||et(e)),z(null)}catch(e){z("Failed to load conversation: ".concat(e.message))}finally{t||eo(!1)}},eP=async()=>{if(!A||0===R.length)return null;try{let e=null==ee?void 0:ee.id;if(!e){let t=R[0],a="New Chat";if(t&&t.content.length>0){let e=t.content.find(e=>"text"===e.type);e&&e.text&&(a=e.text.slice(0,50)+(e.text.length>50?"...":""))}let r={custom_api_config_id:A,title:a},n=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!n.ok)throw Error("Failed to create conversation");let s=await n.json();e=s.id,et(s)}for(let t of R){if(t.id.includes("-")&&t.id.length>20)continue;let a={conversation_id:e,role:t.role,content:t.content};await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})}return ee||eA(!0),e}catch(e){return z("Failed to save conversation: ".concat(e.message)),null}},eB=async e=>{try{if(!(await fetch("/api/chat/conversations?id=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete conversation");(null==ee?void 0:ee.id)===e&&(et(null),I([])),eA(!0)}catch(e){z("Failed to delete conversation: ".concat(e.message))}},ez=async e=>{if(!A)return null;try{let t="New Chat";if(e.content.length>0){let a=e.content.find(e=>"text"===e.type);a&&a.text&&(t=a.text.slice(0,50)+(a.text.length>50?"...":""))}let a={custom_api_config_id:A,title:t},r=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to create conversation");let n=await r.json();return et(n),n.id}catch(e){return z("Failed to create conversation: ".concat(e.message)),null}},eW=async(e,t)=>{try{let a={conversation_id:e,role:t.role,content:t.content},r=await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to save message");return await r.json()}catch(e){}},eF=e=>{H(e),setTimeout(()=>{let e=document.querySelector('textarea[placeholder*="Type a message"]');e&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))},100)},eZ=async()=>{R.length>0&&await eP(),I([]),et(null),H(""),z(null),eR(),ey.reset()},eV=async e=>{if(e===A)return;R.length>0&&await eZ(),_(e);let t=C.find(t=>t.id===e);t&&t.name},eY=async e=>{et(e),I([]),H(""),z(null),eR();let t=(async()=>{if(R.length>0&&!ee)try{await eP()}catch(e){}})();try{await eD(e)}catch(e){z("Failed to load conversation: ".concat(e.message))}await t},eU=(e,t)=>{er(e),es(t)},eJ=()=>{er(null),es("")},eq=async()=>{if(!ea||!en.trim()||!A)return;let e=R.findIndex(e=>e.id===ea);if(-1===e)return;let t=[...R];t[e]={...t[e],content:[{type:"text",text:en.trim()}]};let a=t.slice(0,e+1);if(I(a),er(null),es(""),ee)try{if(R.slice(e+1).length>0){let t=R[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch("/api/chat/messages?conversation_id=".concat(ee.id,"&limit=1&latest=false"));if(e.ok){let a=(await e.json()).find(e=>e.id===t.id);if(a){let e=new Date(a.created_at).getTime(),t=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:ee.id,after_timestamp:e})});t.ok&&await t.json()}}}else{let e=parseInt(t.id)||Date.now(),a=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:ee.id,after_timestamp:e})});a.ok&&await a.json()}}let t=a[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch("/api/chat/messages?id=".concat(t.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:t.content})});if(e.ok)await e.json();else{let t=await e.text();throw Error("Failed to update message: ".concat(t))}}else await eW(ee.id,t);eA(!0),Object.keys(localStorage).filter(e=>e.startsWith("chat_")||e.startsWith("conversation_")).forEach(e=>localStorage.removeItem(e))}catch(e){z("Failed to update conversation: ".concat(e.message))}await eK(a)},eK=async e=>{if(!A||0===e.length)return;P(!0),z(null),ey.startProcessing();let t={custom_api_config_id:A,messages:e.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:W,...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};try{var a,r,n,s,i,o,l;ey.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(t),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}if(ey.analyzeResponseHeaders(e.headers),setTimeout(()=>{W&&ey.markStreaming()},400),W&&e.body){let t=e.body.getReader(),n=new TextDecoder,s=Date.now().toString()+"-assistant",i={id:s,role:"assistant",content:[{type:"text",text:""}]};I(e=>[...e,i]);let o="",l=!1,c=null,d="";for(c=setTimeout(()=>{l||ey.markStreaming()},400);;){let{done:e,value:u}=await t.read();if(e)break;for(let e of n.decode(u,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if("orchestration.progress"===e.object){l=!0,d=e.data.message,c&&(clearTimeout(c),c=null),ey.markOrchestrationStarted(),ej(d);return}if(e.choices&&(null==(r=e.choices[0])||null==(a=r.delta)?void 0:a.content)){let t=e.choices[0].delta.content;o+=t,!l&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(l=!0,c&&(clearTimeout(c),c=null),ey.markOrchestrationStarted()),l&&!d&&eN(t,ey);let a=i.content[0];a.text=o,I(e=>e.map(e=>e.id===s?{...e,content:[a]}:e))}}catch(e){}}}if(c&&clearTimeout(c),o&&ee){let e={...i,content:[{type:"text",text:o}]};o.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||o.includes("*The response will continue automatically in a new message...*")?(await eW(ee.id,e),setTimeout(()=>{ek()},2e3)):await eW(ee.id,e)}}else{let t=await e.json(),a="Could not parse assistant's response.";(null==(i=t.choices)||null==(s=i[0])||null==(n=s.message)?void 0:n.content)?a=t.choices[0].message.content:(null==(l=t.content)||null==(o=l[0])?void 0:o.text)?a=t.content[0].text:"string"==typeof t.text&&(a=t.text);let r={id:Date.now().toString()+"-assistant",role:"assistant",content:[{type:"text",text:a}]};I(e=>[...e,r]),ee&&await eW(ee.id,r)}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};I(t=>[...t,e]),z(t.message)}finally{P(!1),ey.markComplete()}},eX=async(e,t)=>{if(!A||e<0||e>=R.length||"assistant"!==R[e].role)return;P(!0),z(null),ej(""),ey.startProcessing();let a=R.slice(0,e);if(I(a),ee)try{if(R.slice(e).length>0){let t=R[e],a=parseInt(t.id)||Date.now(),r=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:ee.id,from_timestamp:a})});r.ok&&await r.json()}eA(!0)}catch(e){}let r={custom_api_config_id:A,messages:a.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:W,...t&&{specific_api_key_id:t},...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};try{ey.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(r),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText))}ey.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),a=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===a&&(ec(t),eu(!0),em(!1)),setTimeout(()=>{W&&ey.markStreaming()},400),W&&e.body){let t=e.body.getReader(),a=new TextDecoder,r="",o={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:""}]};I(e=>[...e,o]);try{for(;;){let{done:e,value:l}=await t.read();if(e)break;for(let e of a.decode(l,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{var n,s,i;let e=JSON.parse(t);if(null==(i=e.choices)||null==(s=i[0])||null==(n=s.delta)?void 0:n.content){let t=e.choices[0].delta.content;r+=t,t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:")?(ey.markOrchestrationStarted(),eN(t,ey)):eb&&eN(t,ey),I(e=>e.map(e=>e.id===o.id?{...e,content:[{type:"text",text:r}]}:e))}}catch(e){}}}}finally{t.releaseLock()}if(r&&ee){let e={...o,content:[{type:"text",text:r}]};await eW(ee.id,e)}}else{let t=await e.json(),a="";t.choices&&t.choices.length>0&&t.choices[0].message?a=t.choices[0].message.content:t.content&&Array.isArray(t.content)&&t.content.length>0&&(a=t.content[0].text);let r={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:a}]};I(e=>[...e,r]),ee&&await eW(ee.id,r)}}catch(t){let e={id:Date.now().toString()+"-error-retry",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred during retry."}]};I(t=>[...t,e]),z(t.message),ee&&await eW(ee.id,e)}finally{P(!1),ey.markComplete()}},eQ=async e=>{if(e&&e.preventDefault(),!M.trim()&&0===Y.length||!A)return;if("continue"===M.trim().toLowerCase()&&R.length>0){H(""),await ek();return}P(!0),z(null),ej(""),ey.startProcessing(),performance.now();let t=M.trim(),a=[...Y],r=[...J];H(""),eR();let n=[],s=[];if(t&&(n.push({type:"text",text:t}),s.push({type:"text",text:t})),a.length>0)try{for(let e=0;e<a.length;e++){let t=a[e],i=r[e],o=await eM(t);n.push({type:"image_url",image_url:{url:i}}),s.push({type:"image_url",image_url:{url:o}})}}catch(e){z("Failed to process one or more images. Please try again."),P(!1),H(t),U(a),q(r);return}let i={id:Date.now().toString(),role:"user",content:n};I(e=>[...e,i]);let o=null==ee?void 0:ee.id,l=Promise.resolve(o||null);Promise.resolve(),o||ee||(l=ez(i)),l.then(async e=>{e&&await eW(e,i)}).catch(e=>{});let c={custom_api_config_id:A,messages:[...R.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),{role:"user",content:1===s.length&&"text"===s[0].type?s[0].text:s}],stream:W,...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};try{ey.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(c),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}ey.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),a=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===a&&(ec(t),eu(!0),em(!1)),W&&e.body){let t=e.body.getReader(),a=new TextDecoder,r=Date.now().toString()+"-assistant",n={id:r,role:"assistant",content:[{type:"text",text:""}]};I(e=>[...e,n]);let s="",i=!1,o=null,c="";for(o=setTimeout(()=>{i||ey.markStreaming()},400);;){let{done:e,value:l}=await t.read();if(e)break;for(let e of a.decode(l,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{var d,u;let e=JSON.parse(t);if("orchestration.progress"===e.object){i=!0,c=e.data.message,o&&(clearTimeout(o),o=null),ey.markOrchestrationStarted(),ej(c);continue}if(e.choices&&(null==(u=e.choices[0])||null==(d=u.delta)?void 0:d.content)){let t=e.choices[0].delta.content;s+=t,!i&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(i=!0,o&&(clearTimeout(o),o=null),ey.markOrchestrationStarted()),i&&!c&&eN(t,ey);let a=n.content[0];a.text=s,I(e=>e.map(e=>e.id===r?{...e,content:[a]}:e))}}catch(e){}}}if(o&&clearTimeout(o),s){let t={...n,content:[{type:"text",text:s}]},a=e.headers.get("X-Synthesis-Progress");e.headers.get("X-Synthesis-Complete"),s.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||s.includes("*The response will continue automatically in a new message...*")?(l.then(async e=>{e&&await eW(e,t)}),setTimeout(()=>{ek()},null!==a?1e3:2e3)):l.then(async e=>{e&&await eW(e,t)})}}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};I(t=>[...t,e]),z(t.message),l.then(async t=>{t&&await eW(t,e)}).catch(e=>{})}finally{P(!1),ey.markComplete(),(0,w.n4)(ey.stageHistory),performance.now(),l.then(async e=>{e&&!ee&&eA(!0)}).catch(e=>{})}};return(0,r.jsxs)("div",{className:"min-h-screen bg-[#faf8f5] flex",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col transition-all duration-300 ease-in-out",style:{marginLeft:S,marginRight:ex&&!ep?"50%":G?"0px":"320px"},children:[(0,r.jsx)("div",{className:"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out",style:{left:S,right:ex&&!ep?"50%":G?"0px":"320px"},children:(0,r.jsx)("div",{className:"px-6 py-2",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2",children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Connected"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Not Connected"})]})}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{value:A,onChange:e=>eV(e.target.value),disabled:0===C.length,className:"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]",children:[(0,r.jsx)("option",{value:"",children:"Select Router"}),C.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Streaming"}),(0,r.jsx)("button",{onClick:()=>F(!W),className:"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm ".concat(W?"bg-orange-500 shadow-orange-200":"bg-gray-300"),children:(0,r.jsx)("span",{className:"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm ".concat(W?"translate-x-6":"translate-x-1")})})]})]})})}),(0,r.jsx)("div",{className:"flex-1 flex flex-col pt-0 pb-32",children:0!==R.length||ee?(0,r.jsxs)("div",{className:"flex-1 relative ".concat(ex&&!ep?"overflow-visible":"overflow-hidden"),children:[(0,r.jsx)("div",{className:"h-full flex ".concat(ex&&!ep?"justify-start":"justify-center"),children:(0,r.jsx)("div",{ref:Q,className:"w-full h-full overflow-y-auto px-6 transition-all duration-300 ".concat(ex&&!ep?"max-w-2xl -ml-32":"max-w-4xl"),onScroll:e=>{let t=e.currentTarget;V(!(t.scrollHeight-t.scrollTop-t.clientHeight<100)&&R.length>0)},children:(0,r.jsxs)("div",{className:"space-y-6 py-0",children:[ee&&R.length>=50&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("button",{onClick:()=>eD(ee,!0),disabled:eE,className:"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50",children:eE?"Loading...":"Load Earlier Messages"})}),ei&&0===R.length&&(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"flex justify-start",children:[(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0"}),(0,r.jsx)("div",{className:"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]})})]},t))}),R.map((e,t)=>(0,r.jsxs)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"," group ").concat(ex&&!ep?"-ml-96":""," ").concat("assistant"===e.role&&ex&&!ep?"ml-8":""," ").concat(0===t?"pt-3":""),children:["assistant"===e.role&&(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsxs)("div",{className:"".concat("user"===e.role?ex&&!ep?"max-w-[60%]":"max-w-[50%]":ex&&!ep?"max-w-[85%]":"max-w-[75%]"," relative ").concat("user"===e.role?"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm":"assistant"===e.role?"text-gray-900":"system"===e.role?"bg-amber-50 text-amber-800 rounded-xl border border-amber-200":"bg-red-50 text-red-800 rounded-xl border border-red-200"," px-4 py-3 transition-all duration-300"),children:["user"===e.role&&(0,r.jsxs)("div",{className:"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[(0,r.jsx)(h.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message",className:"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer"}),(0,r.jsx)("button",{onClick:()=>eU(e.id,e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n")),className:"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer",title:"Edit message",children:(0,r.jsx)(d,{className:"w-4 h-4 stroke-2"})})]}),"user"!==e.role&&(0,r.jsxs)("div",{className:"absolute -bottom-8 left-0 z-10 flex items-center space-x-2",children:[(0,r.jsx)(h.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message"}),"assistant"===e.role&&A&&(0,r.jsx)(m.A,{configId:A,onRetry:e=>eX(t,e),disabled:D})]}),(0,r.jsx)("div",{className:"space-y-2 chat-message-content",children:"user"===e.role&&ea===e.id?(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:en,onChange:e=>es(e.target.value),className:"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none",placeholder:"Edit your message...",rows:3,autoFocus:!0}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:eq,disabled:!en.trim(),className:"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(s,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Save & Continue"})]}),(0,r.jsxs)("button",{onClick:eJ,className:"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(c,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Cancel"})]})]}),(0,r.jsx)("p",{className:"text-white/70 text-xs",children:"\uD83D\uDCA1 Saving will restart the conversation from this point, removing all messages that came after."})]}):e.content.map((t,a)=>{if("text"===t.type)if("assistant"===e.role)return(0,r.jsx)(u.A,{content:t.text,className:"text-sm"},a);else return(0,r.jsx)("div",{className:"whitespace-pre-wrap break-words leading-relaxed text-sm",children:t.text},a);return"image_url"===t.type?(0,r.jsx)("img",{src:t.image_url.url,alt:"uploaded content",className:"max-w-full max-h-48 rounded-xl shadow-sm"},a):null})})]}),"user"===e.role&&(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})]},e.id)),ed&&el&&ep&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(p.f,{orchestrationComplete:eh,onMaximize:()=>{ew(!0),setTimeout(()=>ew(!1),100)},isCanvasOpen:ex,isCanvasMinimized:ep})}),D&&(0,r.jsxs)("div",{className:"flex justify-start group",children:[(!eb||"typing"===ey.currentStage)&&(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsx)(x.A,{currentStage:ey.currentStage,isStreaming:W&&"typing"===ey.currentStage,orchestrationStatus:eb,onStageChange:e=>{}})]}),ed&&el&&(0,r.jsx)(g.w,{executionId:el,onCanvasStateChange:(e,t)=>{eg(e),ef(t),e&&!t&&$(!0)},forceMaximize:ev,onComplete:e=>{if(null==el?void 0:el.startsWith("test-execution-id"))return void em(!0);em(!0);let t={id:Date.now().toString()+"-orchestration-final",role:"assistant",content:[{type:"text",text:e}]};I(e=>[...e,t]),eu(!1),ec(null),em(!1),(null==ee?void 0:ee.id)&&eW(ee.id,t).catch(e=>{})},onError:e=>{null!=el&&el.startsWith("test-execution-id")||(z("Orchestration error: ".concat(e)),eu(!1),ec(null))}}),(0,r.jsx)("div",{ref:X})]})})}),Z&&(0,r.jsx)("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10",children:(0,r.jsx)("button",{onClick:()=>eI(!0),className:"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group","aria-label":"Scroll to bottom",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]}):(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center px-6 overflow-hidden",children:(0,r.jsx)("div",{className:"w-full mx-auto transition-all duration-300 ".concat(ex&&!ep?"max-w-2xl -ml-32":"max-w-4xl"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:["Welcome",E?" ".concat(E):""," to RouKey"]}),(0,r.jsx)("p",{className:"text-lg text-gray-600 max-w-md mx-auto",children:"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4 w-full max-w-2xl",children:[{id:"write-copy",title:"Write copy",description:"Create compelling marketing content",icon:"✍️",color:"bg-amber-100 text-amber-700",prompt:"Help me write compelling copy for my product landing page"},{id:"image-generation",title:"Image generation",description:"Create visual content descriptions",icon:"\uD83C\uDFA8",color:"bg-blue-100 text-blue-700",prompt:"Help me create detailed prompts for AI image generation"},{id:"create-avatar",title:"Create avatar",description:"Design character personas",icon:"\uD83D\uDC64",color:"bg-green-100 text-green-700",prompt:"Help me create a detailed character avatar for my story"},{id:"write-code",title:"Write code",description:"Generate and debug code",icon:"\uD83D\uDCBB",color:"bg-purple-100 text-purple-700",prompt:"Help me write clean, efficient code for my project"}].map(e=>(0,r.jsxs)("button",{onClick:()=>eF(e.prompt),disabled:!A,className:"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed ".concat(A?"cursor-pointer hover:scale-[1.02]":"cursor-not-allowed"),children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-xl flex items-center justify-center text-xl ".concat(e.color," group-hover:scale-110 transition-transform duration-200"),children:e.icon}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.description})]})]}),(0,r.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4l8 8-8 8M4 12h16"})})})]},e.id))})]})})})}),(0,r.jsx)("div",{className:"fixed bottom-0 z-50 transition-all duration-300 ease-in-out",style:{left:S,right:ex&&!ep?"50%":G?"0px":"320px"},children:(0,r.jsx)("div",{className:"px-6 pt-3 pb-2 flex justify-center",children:(0,r.jsxs)("div",{className:"w-full transition-all duration-300 ".concat(ex&&!ep?"max-w-2xl":"max-w-4xl"),children:[B&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("p",{className:"text-red-800 text-sm font-medium",children:B})]})}),!1,(0,r.jsxs)("form",{onSubmit:eQ,children:[J.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[J.length," image",J.length>1?"s":""," attached"]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>eR(),className:"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium",children:"Clear all"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3",children:J.map((e,t)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square",children:[(0,r.jsx)("img",{src:e,alt:"Preview ".concat(t+1),className:"w-full h-full object-cover"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200"}),(0,r.jsx)("button",{type:"button",onClick:()=>eR(t),className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100","aria-label":"Remove image ".concat(t+1),children:(0,r.jsx)(c,{className:"w-3.5 h-3.5"})})]}),(0,r.jsx)("div",{className:"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium",children:t+1})]},t))})]}),(0,r.jsx)("div",{className:"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300",children:(0,r.jsxs)("div",{className:"flex items-end p-4 space-x-3",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",multiple:!0,onChange:eH,ref:K,className:"hidden",id:"imageUpload"}),(0,r.jsxs)("button",{type:"button",onClick:()=>{var e;return null==(e=K.current)?void 0:e.click()},disabled:Y.length>=10,className:"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 ".concat(Y.length>=10?"text-gray-300 cursor-not-allowed":"text-gray-400 hover:text-orange-500 hover:bg-orange-50"),"aria-label":Y.length>=10?"Maximum 10 images reached":"Attach images",title:Y.length>=10?"Maximum 10 images reached":"Attach images (up to 10)",children:[(0,r.jsx)(o,{className:"w-5 h-5"}),Y.length>0&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:Y.length})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("textarea",{value:M,onChange:e=>H(e.target.value),placeholder:A?"Type a message...":"Select a router first",disabled:!A||D,rows:1,className:"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed",onKeyDown:e=>{"Enter"===e.key&&!e.shiftKey&&(e.preventDefault(),(M.trim()||Y.length>0)&&A&&!D&&eQ())},style:{minHeight:"24px",maxHeight:"120px"},onInput:e=>{let t=e.target;t.style.height="auto",t.style.height=Math.min(t.scrollHeight,120)+"px"}})}),(0,r.jsx)("button",{type:"submit",disabled:!A||D||!M.trim()&&0===Y.length,className:"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0","aria-label":"Send message",title:"Send message",children:D?(0,r.jsx)("svg",{className:"w-5 h-5 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}):(0,r.jsx)(i,{className:"w-5 h-5"})})]})})]})]})})})]}),(0,r.jsx)("div",{className:"fixed top-0 right-0 h-full bg-white shadow-xl transition-all duration-300 ease-in-out z-30 ".concat(G?"w-0 overflow-hidden":"w-80"),style:{transform:G?"translateX(100%)":"translateX(0)",opacity:+!G},children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200/50",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"font-semibold text-gray-900",children:"History"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[eS.length," conversations"]})]})]}),(0,r.jsx)("button",{onClick:()=>$(!G),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105","aria-label":"Toggle history sidebar",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)("div",{className:"p-4 border-b border-gray-200/50",children:(0,r.jsxs)("button",{onClick:eZ,className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,r.jsx)("span",{className:"font-medium",children:"New Chat"})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:eE?(0,r.jsx)("div",{className:"space-y-2 p-4",children:Array.from({length:8}).map((e,t)=>(0,r.jsxs)("div",{className:"p-3 rounded-xl border border-gray-100 animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-200 h-4 w-3/4 rounded mb-2"}),(0,r.jsx)("div",{className:"bg-gray-200 h-3 w-1/2 rounded"})]},t))}):0===eS.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No conversations yet"}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Start chatting to see your history"})]}):(0,r.jsx)(r.Fragment,{children:eS.map(e=>(0,r.jsx)(b,{chat:e,currentConversation:ee,onLoadChat:eY,onDeleteChat:eB},e.id))})}),eC&&(0,r.jsx)("div",{className:"px-4 py-2 bg-orange-50 border-t border-orange-100",children:(0,r.jsxs)("div",{className:"flex items-center text-xs text-orange-600",children:[(0,r.jsx)("svg",{className:"w-3 h-3 mr-1 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Updating..."]})}),eT&&(0,r.jsx)("div",{className:"px-4 py-2 bg-red-50 border-t border-red-100",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-red-600",children:[(0,r.jsx)("span",{children:"Failed to load history"}),(0,r.jsx)("button",{onClick:()=>eA(!0),className:"text-red-700 hover:text-red-800 font-medium",children:"Retry"})]})})]})}),(0,r.jsx)("div",{className:"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out ".concat(G?"opacity-100 scale-100 translate-x-0":"opacity-0 scale-95 translate-x-4 pointer-events-none"),children:(0,r.jsx)("button",{onClick:()=>$(!1),className:"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105","aria-label":"Show history sidebar",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})})]})}b.displayName="ChatHistoryItem"},82880:(e,t,a)=>{"use strict";a.d(t,{B:()=>n.A,Y:()=>r.A});var r=a(58397),n=a(86474)}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,9968,6060,5738,6308,563,2662,8669,8848,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(24766)),_N_E=e.O()}]);