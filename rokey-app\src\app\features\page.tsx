'use client';

import { motion } from 'framer-motion';
import {
  BoltIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CpuChipIcon,
  ArrowPathIcon,
  CloudIcon,
  CodeBracketIcon,
  LightBulbIcon,
  RocketLaunchIcon,
  CogIcon,
  SparklesIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  DocumentTextIcon,
  BeakerIcon,
  AcademicCapIcon,
  CheckCircleIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import InstantLink from '@/components/ui/InstantLink';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'RouKey Features - Advanced AI Gateway with Multi-Role Orchestration | RouKey',
  description: 'Discover RouKey\'s unique features: Multi-Role Orchestration, Smart Cost Optimization, 300+ AI models, unlimited requests, document processing, and advanced analytics. The only AI gateway you\'ll ever need.',
  keywords: 'AI gateway features, multi-role orchestration, cost optimization, AI routing, unlimited API requests, document processing, AI analytics, smart routing',
  openGraph: {
    title: 'RouKey Features - Advanced AI Gateway with Multi-Role Orchestration',
    description: 'Explore RouKey\'s revolutionary features including Multi-Role Orchestration, Smart Cost Optimization, and access to 300+ AI models with unlimited requests.',
    type: 'website',
    url: 'https://roukey.online/features',
  },
  alternates: {
    canonical: 'https://roukey.online/features',
  },
};

// Hero Features - RouKey's Unique Advantages
const heroFeatures = [
  {
    icon: SparklesIcon,
    title: "RouKey's Multi-Role Orchestration",
    description: "The only AI gateway with intelligent multi-agent workflows",
    badge: "UNIQUE",
    color: "from-[#ff6b35] to-[#f7931e]"
  },
  {
    icon: CurrencyDollarIcon,
    title: "RouKey's Smart Cost Optimization",
    description: "Save up to 70% on AI costs with intelligent routing",
    badge: "EXCLUSIVE",
    color: "from-emerald-500 to-emerald-600"
  },
  {
    icon: BoltIcon,
    title: "RouKey's Intelligent Classification",
    description: "AI-powered role detection and automatic routing",
    badge: "PROPRIETARY",
    color: "from-blue-500 to-blue-600"
  }
];

// Core Features
const coreFeatures = [
  {
    icon: CpuChipIcon,
    title: "300+ AI Models",
    description: "Access every major AI model through one unified API",
    stats: "300+ Models",
    details: [
      "OpenAI, Anthropic, Google, Meta",
      "Specialized domain models",
      "Latest model versions",
      "Custom model integration"
    ],
    visual: "🤖"
  },
  {
    icon: UserGroupIcon,
    title: "Multi-Agent Workflows",
    description: "RouKey's advanced orchestration for complex AI tasks",
    stats: "∞ Agents",
    details: [
      "Sequential workflows",
      "Parallel execution",
      "Supervisor patterns",
      "Memory persistence"
    ],
    visual: "🔄"
  },
  {
    icon: DocumentTextIcon,
    title: "Document Processing",
    description: "Built-in PDF, DOCX, and text processing capabilities",
    stats: "All Formats",
    details: [
      "PDF extraction",
      "DOCX processing",
      "RAG integration",
      "Knowledge base training"
    ],
    visual: "📄"
  },
  {
    icon: ChartBarIcon,
    title: "Advanced Analytics",
    description: "Comprehensive insights with real-time performance tracking",
    stats: "Real-time",
    details: [
      "Performance metrics",
      "Cost optimization insights",
      "Quality analytics",
      "Custom reporting"
    ],
    visual: "📊"
  },
  {
    icon: BeakerIcon,
    title: "A/B Testing Router",
    description: "RouKey's continuous optimization for best performance",
    stats: "Auto-Optimize",
    details: [
      "Continuous testing",
      "Performance tracking",
      "Quality metrics",
      "Automatic improvements"
    ],
    visual: "🧪"
  },
  {
    icon: AcademicCapIcon,
    title: "Training System",
    description: "Advanced prompt engineering and model training tools",
    stats: "Smart Training",
    details: [
      "Prompt optimization",
      "Quality feedback",
      "Performance tuning",
      "Custom training"
    ],
    visual: "🎓"
  }
];

// Technical Features
const technicalFeatures = [
  {
    icon: ShieldCheckIcon,
    title: "Enterprise Security",
    description: "Bank-grade security with end-to-end encryption",
    features: ["SOC 2 compliance", "End-to-end encryption", "Audit trails", "RBAC"]
  },
  {
    icon: ArrowPathIcon,
    title: "Auto-Failover",
    description: "99.9% uptime with intelligent failover mechanisms",
    features: ["Smart failover", "Auto-retry", "Health monitoring", "Zero downtime"]
  },
  {
    icon: CloudIcon,
    title: "Global Infrastructure",
    description: "Distributed infrastructure for low latency worldwide",
    features: ["Multi-region", "Edge optimization", "CDN acceleration", "99.9% SLA"]
  },
  {
    icon: CodeBracketIcon,
    title: "Developer-First",
    description: "Built by developers, for developers",
    features: ["RESTful API", "Multiple SDKs", "Interactive docs", "Code examples"]
  }
];

export default function FeaturesPage() {
  return (
    <div className="min-h-screen bg-white">
      <LandingNavbar />

        <main className="pt-20">
          {/* Hero Section */}
          <section className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
            {/* Background decoration */}
            <div className="absolute inset-0 opacity-5">
              <div
                className="absolute inset-0"
                style={{
                  backgroundImage: `
                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                  `,
                  backgroundSize: '60px 60px'
                }}
              ></div>
            </div>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
                  Revolutionary Features for
                  <span className="text-[#ff6b35] block">Next-Gen AI Development</span>
                </h1>
                <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-12">
                  The only AI gateway with Multi-Role Orchestration, Smart Cost Optimization, and unlimited access to 300+ models.
                  Built by a developer who solved the problems you're facing.
                </p>

                {/* Hero Feature Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
                  {heroFeatures.map((feature, index) => (
                    <motion.div
                      key={feature.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className={`relative bg-gradient-to-r ${feature.color} p-6 rounded-xl text-white overflow-hidden group hover:scale-105 transition-transform duration-300`}
                    >
                      <div className="absolute top-2 right-2 bg-white/20 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-bold">
                        {feature.badge}
                      </div>
                      <feature.icon className="h-8 w-8 mb-3" />
                      <h3 className="text-lg font-bold mb-2">{feature.title}</h3>
                      <p className="text-white/90 text-sm">{feature.description}</p>
                    </motion.div>
                  ))}
                </div>

                <InstantLink
                  href="/auth/signup"
                  className="inline-flex items-center px-8 py-4 bg-[#ff6b35] text-white font-bold rounded-xl hover:bg-[#f7931e] transition-colors text-lg"
                >
                  <SparklesIcon className="mr-2 h-5 w-5" />
                  Try All Features Free
                </InstantLink>
              </motion.div>
            </div>
          </section>

          {/* Core Features Section */}
          <section className="py-20 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="text-center mb-16"
              >
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                  Core <span className="text-[#ff6b35]">Capabilities</span>
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Everything you need to build production-ready AI applications with confidence
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {coreFeatures.map((feature, index) => (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl hover:scale-105 transition-all duration-300 group"
                  >
                    {/* Visual Icon */}
                    <div className="text-center mb-6">
                      <div className="text-6xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        {feature.visual}
                      </div>
                      <div className="w-16 h-16 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:rotate-12 transition-transform duration-300">
                        <feature.icon className="w-8 h-8 text-white" />
                      </div>
                    </div>

                    <div className="text-center mb-6">
                      <h3 className="text-xl font-bold text-gray-900 mb-2">{feature.title}</h3>
                      <div className="inline-block bg-[#ff6b35]/10 text-[#ff6b35] px-3 py-1 rounded-full text-sm font-semibold mb-3">
                        {feature.stats}
                      </div>
                      <p className="text-gray-600">{feature.description}</p>
                    </div>

                    <div className="space-y-2">
                      {feature.details.map((detail, idx) => (
                        <div key={idx} className="flex items-center text-sm text-gray-500">
                          <CheckCircleIcon className="w-4 h-4 text-[#ff6b35] mr-2 flex-shrink-0" />
                          {detail}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          {/* Technical Features */}
          <section className="py-20 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="text-center mb-16"
              >
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                  Enterprise-Grade <span className="text-[#ff6b35]">Infrastructure</span>
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  Built for production with security, reliability, and performance at its core
                </p>
              </motion.div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {technicalFeatures.map((feature, index) => (
                  <motion.div
                    key={feature.title}
                    initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300"
                  >
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-xl flex items-center justify-center flex-shrink-0">
                        <feature.icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-xl font-bold text-gray-900 mb-2">{feature.title}</h3>
                        <p className="text-gray-600 mb-4">{feature.description}</p>
                        <div className="grid grid-cols-2 gap-2">
                          {feature.features.map((item, idx) => (
                            <div key={idx} className="flex items-center text-sm text-gray-500">
                              <StarIcon className="w-3 h-3 text-[#ff6b35] mr-1 flex-shrink-0" />
                              {item}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </section>

          {/* Stats Section */}
          <section className="py-20 bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-white"
                >
                  <div className="text-4xl md:text-5xl font-bold mb-2">300+</div>
                  <div className="text-white/90">AI Models</div>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.1 }}
                  className="text-white"
                >
                  <div className="text-4xl md:text-5xl font-bold mb-2">∞</div>
                  <div className="text-white/90">API Requests</div>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.2 }}
                  className="text-white"
                >
                  <div className="text-4xl md:text-5xl font-bold mb-2">70%</div>
                  <div className="text-white/90">Cost Savings</div>
                </motion.div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.3 }}
                  className="text-white"
                >
                  <div className="text-4xl md:text-5xl font-bold mb-2">99.9%</div>
                  <div className="text-white/90">Uptime</div>
                </motion.div>
              </div>
            </div>
          </section>

          {/* CTA Section */}
          <section className="py-32 bg-gradient-to-br from-gray-900 via-black to-gray-900 relative overflow-hidden">
            {/* Background effects */}
            <div className="absolute inset-0">
              <div className="absolute top-20 left-10 w-96 h-96 bg-[#ff6b35] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse"></div>
              <div className="absolute bottom-20 right-10 w-96 h-96 bg-[#f7931e] rounded-full mix-blend-multiply filter blur-3xl opacity-10 animate-pulse" style={{ animationDelay: '2s' }}></div>
            </div>

            <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative">
              <motion.div
                initial={{ opacity: 0, y: 15 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
              >
                <h2 className="text-5xl md:text-6xl font-bold text-white mb-8">
                  Ready to Experience
                  <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e] block">
                    The Future of AI?
                  </span>
                </h2>
                <p className="text-2xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
                  Join thousands of developers who've discovered RouKey's revolutionary Multi-Role Orchestration and Smart Cost Optimization.
                </p>

                {/* Feature highlights */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto">
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <SparklesIcon className="w-8 h-8 text-[#ff6b35] mx-auto mb-3" />
                    <div className="text-white font-semibold">Multi-Role Orchestration</div>
                    <div className="text-gray-400 text-sm">Unique to RouKey</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <CurrencyDollarIcon className="w-8 h-8 text-[#ff6b35] mx-auto mb-3" />
                    <div className="text-white font-semibold">Smart Cost Optimization</div>
                    <div className="text-gray-400 text-sm">Save up to 70%</div>
                  </div>
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                    <BoltIcon className="w-8 h-8 text-[#ff6b35] mx-auto mb-3" />
                    <div className="text-white font-semibold">Unlimited Requests</div>
                    <div className="text-gray-400 text-sm">No limits, ever</div>
                  </div>
                </div>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-6 justify-center">
                  <InstantLink
                    href="/auth/signup?plan=professional"
                    className="inline-flex items-center px-12 py-5 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-bold rounded-2xl hover:shadow-2xl hover:scale-105 transition-all duration-200 text-xl"
                  >
                    <SparklesIcon className="mr-3 h-6 w-6" />
                    Start Free Trial
                  </InstantLink>
                  <InstantLink
                    href="/routing-strategies"
                    className="inline-flex items-center px-12 py-5 bg-white/10 backdrop-blur-sm text-white font-bold rounded-2xl border-2 border-white/20 hover:border-white/40 hover:bg-white/20 transition-all duration-200 text-xl"
                  >
                    <CogIcon className="mr-3 h-6 w-6" />
                    Explore Routing
                  </InstantLink>
                </div>
              </motion.div>
            </div>
          </section>
        </main>

        <Footer />
      </div>
  );
}
