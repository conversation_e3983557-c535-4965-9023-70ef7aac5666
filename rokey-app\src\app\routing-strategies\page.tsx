'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Cog6ToothIcon, 
  BoltIcon, 
  CircleStackIcon, 
  ListBulletIcon, 
  CurrencyDollarIcon, 
  BeakerIcon,
  ArrowRightIcon,
  CheckIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import InstantLink from '@/components/ui/InstantLink';

const routingStrategies = [
  {
    id: 'none',
    name: 'Default Load Balancing',
    shortDescription: 'Automatic load balancing',
    description: "<PERSON><PERSON><PERSON><PERSON> automatically load balances across all keys assigned to this configuration with intra-request retries. No extra setup needed.",
    icon: Cog6ToothIcon,
    features: [
      'Automatic load distribution',
      'Built-in retry mechanisms',
      'Zero configuration required',
      'High availability'
    ],
    useCase: 'Perfect for simple setups where you want reliable distribution across multiple API keys without complex routing logic.',
    performance: 'Excellent reliability with automatic failover',
    complexity: 'Beginner',
    color: 'from-gray-500 to-gray-600'
  },
  {
    id: 'intelligent_role',
    name: "<PERSON><PERSON><PERSON><PERSON>'s Intelligent Role Routing",
    shortDescription: 'AI-powered role classification',
    description: "<PERSON><PERSON><PERSON><PERSON> uses advanced AI to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.",
    icon: BoltIcon,
    features: [
      "RouKey's AI-powered classification",
      'Dynamic role assignment',
      'Context-aware routing',
      'Fallback to default model'
    ],
    useCase: 'Ideal for applications with diverse use cases like coding, writing, analysis, and general chat.',
    performance: 'Superior accuracy with RouKey\'s proprietary classification',
    complexity: 'Intermediate',
    color: 'from-[#ff6b35] to-[#f7931e]',
    featured: true
  },
  {
    id: 'complexity_round_robin',
    name: "RouKey's Complexity-Based Routing",
    shortDescription: 'Route by prompt complexity',
    description: 'RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.',
    icon: CircleStackIcon,
    features: [
      'Intelligent complexity analysis',
      'Optimized model selection',
      'Cost-performance balance',
      'Proximal level fallback'
    ],
    useCase: 'Perfect for cost optimization - route simple tasks to cheaper models and complex tasks to premium models.',
    performance: 'Optimal cost-performance ratio',
    complexity: 'Advanced',
    color: 'from-blue-500 to-blue-600'
  },
  {
    id: 'strict_fallback',
    name: 'Strict Fallback Strategy',
    shortDescription: 'Ordered failover sequence',
    description: 'Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.',
    icon: ListBulletIcon,
    features: [
      'Predictable routing order',
      'Guaranteed fallback chain',
      'Manual priority control',
      'Reliable failover'
    ],
    useCase: 'Best for scenarios where you have a preferred model hierarchy and want guaranteed fallback behavior.',
    performance: 'Highly predictable with manual control',
    complexity: 'Intermediate',
    color: 'from-green-500 to-green-600'
  },
  {
    id: 'cost_optimized',
    name: "RouKey's Smart Cost Optimization",
    shortDescription: 'Intelligent cost-performance balance',
    description: 'RouKey intelligently balances cost and performance by routing simple tasks to cheaper models and complex tasks to premium models. Maximizes savings while ensuring quality.',
    icon: CurrencyDollarIcon,
    features: [
      "RouKey's learning algorithms",
      'Dynamic cost optimization',
      'Quality preservation',
      'Automatic model selection'
    ],
    useCase: 'Essential for production applications where cost control is critical but quality cannot be compromised.',
    performance: 'Maximum cost savings with quality assurance',
    complexity: 'Advanced',
    color: 'from-emerald-500 to-emerald-600',
    featured: true
  },
  {
    id: 'ab_routing',
    name: "RouKey's A/B Testing Router",
    shortDescription: 'Continuous model optimization',
    description: 'RouKey continuously tests different models with 15% of requests to find the best performing models for your specific use cases. Automatically optimizes routing based on quality and cost metrics.',
    icon: BeakerIcon,
    features: [
      'Continuous optimization',
      'Data-driven decisions',
      'Performance tracking',
      'Automatic improvements'
    ],
    useCase: 'Perfect for applications that want to continuously improve performance and find the best models for their specific use cases.',
    performance: 'Self-improving performance over time',
    complexity: 'Advanced',
    color: 'from-purple-500 to-purple-600'
  }
];

export default function RoutingStrategiesPage() {
  const [selectedStrategy, setSelectedStrategy] = useState(routingStrategies[1]); // Default to intelligent role

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <div className="bg-gradient-to-br from-gray-50 to-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-4xl md:text-5xl font-bold text-black mb-6"
            >
              RouKey's Advanced <span className="text-[#ff6b35]">Routing Strategies</span>
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="text-xl text-gray-600 max-w-3xl mx-auto mb-8"
            >
              Choose the perfect routing strategy for your AI applications. From simple load balancing to advanced multi-role orchestration.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <InstantLink
                href="/pricing"
                className="inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors"
              >
                View Pricing Plans
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </InstantLink>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Strategy Grid */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Strategy Cards */}
          <div className="lg:col-span-2">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {routingStrategies.map((strategy, index) => (
                <motion.div
                  key={strategy.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => setSelectedStrategy(strategy)}
                  className={`relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                    selectedStrategy.id === strategy.id
                      ? 'border-[#ff6b35] bg-[#ff6b35]/5'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {strategy.featured && (
                    <div className="absolute -top-3 -right-3 bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-semibold">
                      Featured
                    </div>
                  )}
                  
                  <div className="flex items-start space-x-4">
                    <div className={`p-3 rounded-lg bg-gradient-to-r ${strategy.color}`}>
                      <strategy.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-bold text-black mb-2">{strategy.name}</h3>
                      <p className="text-gray-600 text-sm mb-3">{strategy.shortDescription}</p>
                      <div className="flex items-center justify-between">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          strategy.complexity === 'Beginner' ? 'bg-green-100 text-green-800' :
                          strategy.complexity === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {strategy.complexity}
                        </span>
                        {selectedStrategy.id === strategy.id && (
                          <CheckIcon className="h-5 w-5 text-[#ff6b35]" />
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Strategy Details */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <motion.div
                key={selectedStrategy.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white border border-gray-200 rounded-xl p-6 shadow-lg"
              >
                <div className="flex items-center space-x-3 mb-4">
                  <div className={`p-3 rounded-lg bg-gradient-to-r ${selectedStrategy.color}`}>
                    <selectedStrategy.icon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-black">{selectedStrategy.name}</h3>
                    <p className="text-gray-600">{selectedStrategy.shortDescription}</p>
                  </div>
                </div>

                <p className="text-gray-700 mb-6">{selectedStrategy.description}</p>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-black mb-2">Key Features</h4>
                    <ul className="space-y-2">
                      {selectedStrategy.features.map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <CheckIcon className="h-4 w-4 text-[#ff6b35]" />
                          <span className="text-gray-700 text-sm">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-semibold text-black mb-2">Best Use Case</h4>
                    <p className="text-gray-700 text-sm">{selectedStrategy.useCase}</p>
                  </div>

                  <div>
                    <h4 className="font-semibold text-black mb-2">Performance</h4>
                    <p className="text-gray-700 text-sm">{selectedStrategy.performance}</p>
                  </div>
                </div>

                <div className="mt-6 pt-6 border-t border-gray-200">
                  <InstantLink
                    href="/pricing"
                    className="w-full inline-flex items-center justify-center px-4 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors"
                  >
                    <SparklesIcon className="mr-2 h-5 w-5" />
                    Get Started
                  </InstantLink>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] py-16">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Optimize Your AI Routing?
          </h2>
          <p className="text-xl text-white/90 mb-8">
            Start with any strategy and switch anytime. No configuration required.
          </p>
          <InstantLink
            href="/auth/signup?plan=professional"
            className="inline-flex items-center px-8 py-4 bg-white text-[#ff6b35] font-bold rounded-xl hover:bg-gray-50 transition-colors text-lg"
          >
            Start Building Now
            <ArrowRightIcon className="ml-3 h-5 w-5" />
          </InstantLink>
        </div>
      </div>
    </div>
  );
}
