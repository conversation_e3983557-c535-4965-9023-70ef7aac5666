(()=>{var e={};e.id=1529,e.ids=[1489,1529],e.modules={507:(e,t,r)=>{"use strict";r.d(t,{Dc:()=>i,p2:()=>s});let s=[{id:"general_chat",name:"<PERSON> Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."}],i=e=>s.find(t=>t.id===e)},2507:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a,createSupabaseServerClientOnRequest:()=>n});var s=r(34386),i=r(44999);async function n(){let e=await (0,i.UL)();return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,s){try{e.set({name:t,value:r,...s})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function a(e){return(0,s.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47990:()=>{},49722:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>y,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var i=r(96559),n=r(48088),a=r(37719),o=r(32190),u=r(2507),c=r(507);async function d(e,{params:t}){let r=(0,u.Q)(e),{apiKeyId:s}=await t;if(!s)return o.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{data:e,error:t}=await r.from("api_key_role_assignments").select("role_name, created_at").eq("api_key_id",s);if(t)return o.NextResponse.json({error:"Failed to fetch role assignments",details:t.message},{status:500});let i=e.map(e=>{let t=(0,c.Dc)(e.role_name);return{...e,role_details:t||{id:e.role_name,name:e.role_name,description:"Custom role (details managed globally)"}}});return o.NextResponse.json(i||[],{status:200})}catch(e){return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}async function p(e,{params:t}){let r=(0,u.Q)(e),{apiKeyId:s}=await t,{data:{session:i},error:n}=await r.auth.getSession();if(n||!i?.user)return o.NextResponse.json({error:"Unauthorized: You must be logged in to assign roles."},{status:401});let a=i.user.id;if(!s)return o.NextResponse.json({error:"API Key ID is required"},{status:400});try{let{role_name:t}=await e.json();if(!t||"string"!=typeof t)return o.NextResponse.json({error:"Role name (role_id) is required and must be a string"},{status:400});let{data:i,error:n}=await r.from("api_keys").select(`
        custom_api_config_id,
        custom_api_configs ( user_id )
      `).eq("id",s).single();if(n||!i)return o.NextResponse.json({error:"API Key not found or failed to fetch its details"},{status:404});let u=i.custom_api_configs?.user_id;if(!u)return o.NextResponse.json({error:"Could not determine the config owner for the API Key."},{status:500});if(u&&a!==u)return o.NextResponse.json({error:"Forbidden. You do not own the configuration this API key belongs to."},{status:403});let d=c.p2.some(e=>e.id===t),p=!1;if(!d){let{data:e,error:s}=await r.from("user_custom_roles").select("id").eq("user_id",a).eq("role_id",t).maybeSingle();if(s)return o.NextResponse.json({error:"Error validating role.",details:s.message},{status:500});e&&(p=!0)}if(!d&&!p)return o.NextResponse.json({error:`Invalid role_name: ${t}. Not a predefined role or a custom role you own.`},{status:400});let{custom_api_config_id:l}=i,{data:g,error:m}=await r.from("api_key_role_assignments").insert({api_key_id:s,custom_api_config_id:l,role_name:t}).select().single();if(m){if("23505"===m.code){if(m.message.includes("unique_api_key_role"))return o.NextResponse.json({error:"This API key already has this role assigned.",details:m.message},{status:409});if(m.message.includes("unique_role_per_custom_config"))return o.NextResponse.json({error:"This role is already assigned to another API key in this Custom Model (config). Check unique_role_per_custom_config constraint.",details:m.message},{status:409});return o.NextResponse.json({error:"Failed to assign role: This role may already be assigned in a way that violates a uniqueness constraint.",details:m.message,code:m.code},{status:409})}return o.NextResponse.json({error:"Failed to assign role to API key",details:m.message},{status:500})}return o.NextResponse.json(g,{status:201})}catch(e){if("SyntaxError"===e.name)return o.NextResponse.json({error:"Invalid request body: Malformed JSON."},{status:400});return o.NextResponse.json({error:"An unexpected error occurred",details:e.message},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/keys/[apiKeyId]/roles/route",pathname:"/api/keys/[apiKeyId]/roles",filename:"route",bundlePath:"app/api/keys/[apiKeyId]/roles/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\keys\\[apiKeyId]\\roles\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:m,serverHooks:y}=l;function f(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:m})}},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4447,580,9398,3410],()=>r(49722));module.exports=s})();