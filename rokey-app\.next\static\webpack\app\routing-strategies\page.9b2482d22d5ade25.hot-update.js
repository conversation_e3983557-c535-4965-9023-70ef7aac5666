"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/routing-strategies/page",{

/***/ "(app-pages-browser)/./src/app/routing-strategies/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/routing-strategies/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingStrategiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst routingStrategies = [\n    {\n        id: 'none',\n        name: 'Default Load Balancing',\n        shortDescription: 'Automatic load balancing',\n        description: \"RouKey automatically load balances across all keys assigned to this configuration with intra-request retries. No extra setup needed.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        features: [\n            'Automatic load distribution',\n            'Built-in retry mechanisms',\n            'Zero configuration required',\n            'High availability'\n        ],\n        useCase: 'Perfect for simple setups where you want reliable distribution across multiple API keys without complex routing logic.',\n        performance: 'Excellent reliability with automatic failover',\n        complexity: 'Beginner',\n        color: 'from-gray-500 to-gray-600'\n    },\n    {\n        id: 'intelligent_role',\n        name: \"RouKey's Intelligent Role Routing\",\n        shortDescription: 'AI-powered role classification',\n        description: \"RouKey uses advanced AI to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        features: [\n            \"RouKey's AI-powered classification\",\n            'Dynamic role assignment',\n            'Context-aware routing',\n            'Fallback to default model'\n        ],\n        useCase: 'Ideal for applications with diverse use cases like coding, writing, analysis, and general chat.',\n        performance: 'Superior accuracy with RouKey\\'s proprietary classification',\n        complexity: 'Intermediate',\n        color: 'from-[#ff6b35] to-[#f7931e]',\n        featured: true\n    },\n    {\n        id: 'complexity_round_robin',\n        name: \"RouKey's Complexity-Based Routing\",\n        shortDescription: 'Route by prompt complexity',\n        description: 'RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        features: [\n            'Intelligent complexity analysis',\n            'Optimized model selection',\n            'Cost-performance balance',\n            'Proximal level fallback'\n        ],\n        useCase: 'Perfect for cost optimization - route simple tasks to cheaper models and complex tasks to premium models.',\n        performance: 'Optimal cost-performance ratio',\n        complexity: 'Advanced',\n        color: 'from-blue-500 to-blue-600'\n    },\n    {\n        id: 'strict_fallback',\n        name: 'Strict Fallback Strategy',\n        shortDescription: 'Ordered failover sequence',\n        description: 'Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        features: [\n            'Predictable routing order',\n            'Guaranteed fallback chain',\n            'Manual priority control',\n            'Reliable failover'\n        ],\n        useCase: 'Best for scenarios where you have a preferred model hierarchy and want guaranteed fallback behavior.',\n        performance: 'Highly predictable with manual control',\n        complexity: 'Intermediate',\n        color: 'from-green-500 to-green-600'\n    },\n    {\n        id: 'cost_optimized',\n        name: \"RouKey's Smart Cost Optimization\",\n        shortDescription: 'Intelligent cost-performance balance',\n        description: 'RouKey intelligently balances cost and performance by routing simple tasks to cheaper models and complex tasks to premium models. Maximizes savings while ensuring quality.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        features: [\n            \"RouKey's learning algorithms\",\n            'Dynamic cost optimization',\n            'Quality preservation',\n            'Automatic model selection'\n        ],\n        useCase: 'Essential for production applications where cost control is critical but quality cannot be compromised.',\n        performance: 'Maximum cost savings with quality assurance',\n        complexity: 'Advanced',\n        color: 'from-emerald-500 to-emerald-600',\n        featured: true\n    },\n    {\n        id: 'ab_routing',\n        name: \"RouKey's A/B Testing Router\",\n        shortDescription: 'Continuous model optimization',\n        description: 'RouKey continuously tests different models with 15% of requests to find the best performing models for your specific use cases. Automatically optimizes routing based on quality and cost metrics.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        features: [\n            'Continuous optimization',\n            'Data-driven decisions',\n            'Performance tracking',\n            'Automatic improvements'\n        ],\n        useCase: 'Perfect for applications that want to continuously improve performance and find the best models for their specific use cases.',\n        performance: 'Self-improving performance over time',\n        complexity: 'Advanced',\n        color: 'from-purple-500 to-purple-600'\n    }\n];\nfunction RoutingStrategiesPage() {\n    _s();\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(routingStrategies[1]); // Default to intelligent role\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-gray-50 to-white border-b border-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.h1, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"text-4xl md:text-5xl font-bold text-black mb-6\",\n                                children: [\n                                    \"RouKey's Advanced \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-[#ff6b35]\",\n                                        children: \"Routing Strategies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                children: \"Choose the perfect routing strategy for your AI applications. From simple load balancing to advanced multi-role orchestration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/pricing\",\n                                    className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors\",\n                                    children: [\n                                        \"View Pricing Plans\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: routingStrategies.map((strategy, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        onClick: ()=>setSelectedStrategy(strategy),\n                                        className: \"relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 \".concat(selectedStrategy.id === strategy.id ? 'border-[#ff6b35] bg-[#ff6b35]/5' : 'border-gray-200 hover:border-gray-300'),\n                                        children: [\n                                            strategy.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-3 -right-3 bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg bg-gradient-to-r \".concat(strategy.color),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(strategy.icon, {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-black mb-2\",\n                                                                children: strategy.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 199,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm mb-3\",\n                                                                children: strategy.shortDescription\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(strategy.complexity === 'Beginner' ? 'bg-green-100 text-green-800' : strategy.complexity === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),\n                                                                        children: strategy.complexity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                        lineNumber: 202,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    selectedStrategy.id === strategy.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-[#ff6b35]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                        lineNumber: 210,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, strategy.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_9__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"bg-white border border-gray-200 rounded-xl p-6 shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-gradient-to-r \".concat(selectedStrategy.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(selectedStrategy.icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-black\",\n                                                            children: selectedStrategy.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 234,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: selectedStrategy.shortDescription\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 233,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-6\",\n                                            children: selectedStrategy.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Key Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 243,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: selectedStrategy.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-[#ff6b35]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                            lineNumber: 247,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-700 text-sm\",\n                                                                            children: feature\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                            lineNumber: 248,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                    lineNumber: 246,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 244,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Best Use Case\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 255,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-sm\",\n                                                            children: selectedStrategy.useCase\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Performance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-sm\",\n                                                            children: selectedStrategy.performance\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 261,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/pricing\",\n                                                className: \"w-full inline-flex items-center justify-center px-4 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 270,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Get Started\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, selectedStrategy.id, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 171,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"Ready to Optimize Your AI Routing?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-white/90 mb-8\",\n                            children: \"Start with any strategy and switch anytime. No configuration required.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            href: \"/auth/signup?plan=professional\",\n                            className: \"inline-flex items-center px-8 py-4 bg-white text-[#ff6b35] font-bold rounded-xl hover:bg-gray-50 transition-colors text-lg\",\n                            children: [\n                                \"Start Building Now\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"ml-3 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingStrategiesPage, \"4ayf+QIiw8AtwaO/Cyi5ZAptEMo=\");\n_c = RoutingStrategiesPage;\nvar _c;\n$RefreshReg$(_c, \"RoutingStrategiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/routing-strategies/page.tsx\n"));

/***/ })

});