(()=>{var e={};e.id=2778,e.ids=[2778],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14689:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(43210);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m4.5 12.75 6 6 9-13.5"}))})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22461:(e,t,a)=>{Promise.resolve().then(a.bind(a,47677))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},47677:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var r=a(60687),s=a(43210),n=a(85814),o=a.n(n),i=a(30474),l=a(52535),d=a(94257),c=a(66524),p=a(14689),m=a(56878),x=a(79481),f=a(16189);function u(){let[e,t]=(0,s.useState)({firstName:"",lastName:"",email:"",password:"",confirmPassword:""}),[a,n]=(0,s.useState)(!1),[u,g]=(0,s.useState)(!1),[b,h]=(0,s.useState)(!1),[$,w]=(0,s.useState)(""),[y,k]=(0,s.useState)(!1),v=(0,f.useRouter)(),j=(0,f.useSearchParams)(),N=(0,x.u)(),C=j.get("plan"),P=e=>{t(t=>({...t,[e.target.name]:e.target.value}))},z=async t=>{if(t.preventDefault(),h(!0),w(""),e.password!==e.confirmPassword){w("Passwords do not match"),h(!1);return}if(e.password.length<8){w("Password must be at least 8 characters long"),h(!1);return}if(!y){w("Please agree to the Terms of Service and Privacy Policy"),h(!1);return}try{let{data:t,error:a}=await N.auth.signUp({email:e.email,password:e.password,options:{data:{first_name:e.firstName,last_name:e.lastName,full_name:`${e.firstName} ${e.lastName}`,plan:C||"professional",payment_status:"pending"}}});if(a){if(a.message.includes("already registered"))try{let{data:t,error:a}=await N.auth.signInWithPassword({email:e.email,password:e.password});if(t.user&&t.user.user_metadata?.payment_status==="pending"){await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"PENDING_USER_RETRY",userId:t.user.id,email:e.email,selectedPlan:C,message:"Allowing pending user to retry checkout - user is now signed in",sessionExists:!!t.session})}).catch(()=>{});let a=null,r=0;for(;!a&&r<3;){let{data:{session:e}}=await N.auth.getSession();a=e,r++,!a&&r<3&&await new Promise(e=>setTimeout(e,1e3))}a&&a.user?v.push(`/checkout?plan=${C}&user_id=${a.user.id}&email=${encodeURIComponent(e.email)}`):(w("Failed to establish session. Please try signing in manually."),v.push(`/auth/signin?plan=${C}&email=${encodeURIComponent(e.email)}&message=session_failed`))}else window.location.href=`/auth/signin?plan=${C}&message=account_exists`}catch(e){window.location.href=`/auth/signin?plan=${C}&message=account_exists`}else w(a.message);return}if(!t.user)return void w("Failed to create account. Please try again.");await fetch("/api/debug/checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({action:"USER_CREATED_PAYMENT_PENDING",userId:t.user.id,email:e.email,selectedPlan:C,redirectUrl:`/checkout?plan=${C}&user_id=${t.user.id}&email=${encodeURIComponent(e.email)}`,paymentStatus:"pending",message:"User created successfully, user should be automatically signed in"})}).catch(()=>{});let{data:{session:r}}=await N.auth.getSession();r&&r.user?setTimeout(()=>{window.location.href=`/checkout?plan=${C}&user_id=${r.user.id}&email=${encodeURIComponent(e.email)}`},500):window.location.href=`/auth/signin?plan=${C}&checkout_user_id=${t.user.id}&email=${encodeURIComponent(e.email)}&message=account_created`}catch(e){w(e.message||"Failed to process signup. Please try again.")}finally{h(!1)}},S=[{text:"At least 8 characters",met:e.password.length>=8},{text:"Contains uppercase letter",met:/[A-Z]/.test(e.password)},{text:"Contains lowercase letter",met:/[a-z]/.test(e.password)},{text:"Contains number",met:/\d/.test(e.password)}];return(0,r.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8",children:[(0,r.jsx)(m.A,{gridSize:50,opacity:.064,color:"#000000",variant:"subtle",animated:!0,className:"fixed inset-0"}),(0,r.jsx)("div",{className:"absolute inset-0",children:(0,r.jsx)("div",{className:"absolute inset-0",style:{backgroundImage:"radial-gradient(circle, rgba(255, 107, 53, 0.12) 1px, transparent 1px)",backgroundSize:"100px 100px",mask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)",WebkitMask:"radial-gradient(ellipse 70% 70% at center, black 30%, transparent 70%)"}})}),(0,r.jsx)("div",{className:"relative z-10 w-full max-w-6xl mx-auto",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,r.jsx)(l.P.div,{initial:{opacity:0,x:-50},animate:{opacity:1,x:0},transition:{duration:.8},className:"hidden lg:block",children:(0,r.jsxs)("div",{className:"bg-gradient-to-br from-[#ff6b35] to-[#f7931e] rounded-3xl p-12 text-white relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",style:{backgroundImage:`
                    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                  `,backgroundSize:"30px 30px"}}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mb-6 backdrop-blur-sm",children:(0,r.jsx)(i.default,{src:"/roukey_logo.png",alt:"RouKey",width:48,height:48,className:"w-12 h-12 object-contain",priority:!0})}),(0,r.jsx)("h1",{className:"text-4xl font-bold mb-4",children:"Join RouKey Today"}),(0,r.jsxs)("p",{className:"text-xl text-white/90 mb-8",children:["Get started with ",(0,r.jsx)("span",{className:"font-bold",children:"UNLIMITED"})," access to 300+ AI models"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,r.jsx)("span",{className:"text-white/90",children:"No Request Limits"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,r.jsx)("span",{className:"text-white/90",children:"300+ AI Models"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"}),(0,r.jsx)("span",{className:"text-white/90",children:"Enterprise Security"})]})]})]})]})}),(0,r.jsxs)(l.P.div,{initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{duration:.8,delay:.2},className:"w-full max-w-md mx-auto lg:mx-0",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[(0,r.jsxs)(o(),{href:"/",className:"inline-flex items-center space-x-3 mb-8 justify-center lg:justify-start",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg border border-gray-100 p-1",children:(0,r.jsx)(i.default,{src:"/roukey_logo.png",alt:"RouKey",width:40,height:40,className:"w-full h-full object-contain",priority:!0})}),(0,r.jsx)("span",{className:"text-3xl font-bold text-black",children:"RouKey"})]}),(0,r.jsx)("h2",{className:"text-4xl font-bold text-black mb-3",children:"Sign Up"}),(0,r.jsx)("p",{className:"text-gray-600 text-lg",children:"Create your AI gateway account"}),(0,r.jsxs)("div",{className:"flex gap-2 mt-2",children:[(0,r.jsx)("button",{onClick:async()=>{await N.auth.signOut(),localStorage.clear(),window.location.reload()},className:"text-xs text-gray-400 hover:text-gray-600",children:"\uD83D\uDD27 Clear Session"}),(0,r.jsx)("button",{onClick:()=>{w(""),t({firstName:"",lastName:"",email:"",password:"",confirmPassword:""})},className:"text-xs text-gray-400 hover:text-gray-600",children:"\uD83D\uDDD1️ Clear Form"})]}),C&&(0,r.jsxs)("div",{className:"mt-4 p-4 bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 border border-[#ff6b35]/20 rounded-xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"}),(0,r.jsxs)("span",{className:"text-[#ff6b35] font-semibold text-lg",children:[C.charAt(0).toUpperCase()+C.slice(1)," Plan Selected"]}),(0,r.jsx)("div",{className:"w-2 h-2 bg-[#ff6b35] rounded-full"})]}),(0,r.jsx)("p",{className:"text-center text-gray-600 text-sm mt-1",children:"You'll be redirected to checkout after creating your account"}),(0,r.jsx)("div",{className:"text-center mt-2",children:(0,r.jsx)(o(),{href:"/pricing",className:"text-[#ff6b35] hover:text-[#e55a2b] text-sm font-medium transition-colors",children:"Change plan"})})]})]}),(0,r.jsxs)("div",{className:"bg-white rounded-3xl shadow-xl border border-gray-100 p-8 backdrop-blur-sm relative overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 opacity-5",style:{backgroundImage:`
                    linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
                  `,backgroundSize:"20px 20px"}}),(0,r.jsxs)("div",{className:"relative z-10",children:[$&&(0,r.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-xl",children:[(0,r.jsx)("p",{className:"text-red-600 text-sm",children:$}),$.includes("already registered")&&(0,r.jsx)("div",{className:"mt-3",children:(0,r.jsx)(o(),{href:"/auth/signin",className:"text-[#ff6b35] hover:text-[#e55a2b] font-semibold text-sm transition-colors",children:"→ Go to Sign In page"})})]}),(0,r.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDC64 First Name"}),(0,r.jsx)("input",{id:"firstName",name:"firstName",type:"text",required:!0,value:e.firstName,onChange:P,className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"John"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDC64 Last Name"}),(0,r.jsx)("input",{id:"lastName",name:"lastName",type:"text",required:!0,value:e.lastName,onChange:P,className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Doe"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDCE7 Email Address"}),(0,r.jsx)("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:e.email,onChange:P,className:"w-full px-5 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"<EMAIL>"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"password",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDD12 Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{id:"password",name:"password",type:a?"text":"password",required:!0,value:e.password,onChange:P,className:"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Create a strong password"}),(0,r.jsx)("button",{type:"button",onClick:()=>n(!a),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors",children:a?(0,r.jsx)(d.A,{className:"h-5 w-5"}):(0,r.jsx)(c.A,{className:"h-5 w-5"})})]}),e.password&&(0,r.jsx)("div",{className:"mt-3 p-4 bg-gray-50 rounded-xl space-y-2",children:S.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(p.A,{className:`h-4 w-4 mr-3 ${e.met?"text-green-500":"text-gray-300"}`}),(0,r.jsx)("span",{className:e.met?"text-green-600 font-medium":"text-gray-500",children:e.text})]},t))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"confirmPassword",className:"block text-sm font-semibold text-gray-800 mb-3",children:"\uD83D\uDD12 Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{id:"confirmPassword",name:"confirmPassword",type:u?"text":"password",required:!0,value:e.confirmPassword,onChange:P,className:"w-full px-5 py-4 pr-14 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-[#ff6b35] focus:border-[#ff6b35] transition-all duration-300 text-gray-900 placeholder-gray-500 bg-gray-50 focus:bg-white",placeholder:"Confirm your password"}),(0,r.jsx)("button",{type:"button",onClick:()=>g(!u),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-[#ff6b35] transition-colors",children:u?(0,r.jsx)(d.A,{className:"h-5 w-5"}):(0,r.jsx)(c.A,{className:"h-5 w-5"})})]})]}),(0,r.jsxs)("div",{className:"flex items-start p-4 bg-gray-50 rounded-xl",children:[(0,r.jsx)("input",{id:"terms",name:"terms",type:"checkbox",checked:y,onChange:e=>k(e.target.checked),className:"h-5 w-5 text-[#ff6b35] focus:ring-[#ff6b35] border-gray-300 rounded-lg mt-1"}),(0,r.jsxs)("label",{htmlFor:"terms",className:"ml-3 block text-sm text-gray-700",children:["I agree to the"," ",(0,r.jsx)(o(),{href:"/terms",className:"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Terms of Service"})," ","and"," ",(0,r.jsx)(o(),{href:"/privacy",className:"text-[#ff6b35] hover:text-[#e55a2b] font-semibold transition-colors",children:"Privacy Policy"})]})]}),(0,r.jsx)("button",{type:"submit",disabled:b,className:"w-full bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white py-4 px-6 rounded-xl font-bold text-lg hover:from-[#e55a2b] hover:to-[#e8851a] focus:ring-4 focus:ring-[#ff6b35]/30 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-[1.02]",children:b?(0,r.jsxs)("div",{className:"flex items-center justify-center",children:[(0,r.jsx)("div",{className:"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Creating account..."]}):"Create Account"})]})]})]}),(0,r.jsx)("div",{className:"text-center mt-8",children:(0,r.jsxs)("p",{className:"text-gray-600 text-lg",children:["Already have an account?"," ",(0,r.jsx)(o(),{href:"/auth/signin",className:"text-[#ff6b35] hover:text-[#e55a2b] font-bold transition-colors",children:"Sign in"})]})})]})]})})]})}function g(){return(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Loading..."})]})}),children:(0,r.jsx)(u,{})})}},52317:(e,t,a)=>{Promise.resolve().then(a.bind(a,94796))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56878:(e,t,a)=>{"use strict";a.d(t,{A:()=>o});var r=a(60687),s=a(76180),n=a.n(s);function o({className:e="",gridSize:t=40,opacity:a=.1,color:s="#000000",animated:o=!1,glowEffect:i=!1,variant:l="subtle"}){return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{style:{...(()=>{let e=(e,t)=>"#000000"===e?`rgba(0, 0, 0, ${t})`:"#ffffff"===e?`rgba(255, 255, 255, ${t})`:"#ff6b35"===e?`rgba(255, 107, 53, ${t})`:`${e}${Math.round(255*t).toString(16).padStart(2,"0")}`,r=3.2*a*.8;switch(l){case"tech":return{backgroundImage:`
            linear-gradient(${e(s,r)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,r)} 1px, transparent 1px),
            radial-gradient(circle at 50% 50%, ${e(s,.5*r)} 2px, transparent 2px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${4*t}px ${4*t}px`,animation:o?"tech-grid-move 30s linear infinite":"none",mask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 90% 90% at center, black 10%, transparent 85%),
            linear-gradient(to right, transparent 0%, black 8%, black 92%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 8%, black 92%, transparent 100%)
          `,WebkitMaskComposite:"source-in"};case"premium":return{backgroundImage:`
            linear-gradient(${e(s,r)} 0.5px, transparent 0.5px),
            linear-gradient(90deg, ${e(s,r)} 0.5px, transparent 0.5px),
            linear-gradient(${e(s,.7*r)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,.7*r)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px, ${t}px ${t}px, ${5*t}px ${5*t}px, ${5*t}px ${5*t}px`,animation:o?"premium-grid-float 40s ease-in-out infinite":"none",mask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 85% 85% at center, black 15%, transparent 80%),
            linear-gradient(to right, transparent 2%, black 12%, black 88%, transparent 98%),
            linear-gradient(to bottom, transparent 2%, black 12%, black 88%, transparent 98%)
          `,WebkitMaskComposite:"source-in"};default:return{backgroundImage:`
            linear-gradient(${e(s,r)} 1px, transparent 1px),
            linear-gradient(90deg, ${e(s,r)} 1px, transparent 1px)
          `,backgroundSize:`${t}px ${t}px`,animation:o?"subtle-grid-drift 25s linear infinite":"none",mask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,maskComposite:"intersect",WebkitMask:`
            radial-gradient(ellipse 95% 95% at center, black 5%, transparent 90%),
            linear-gradient(to right, transparent 0%, black 5%, black 95%, transparent 100%),
            linear-gradient(to bottom, transparent 0%, black 5%, black 95%, transparent 100%)
          `,WebkitMaskComposite:"source-in"}}})(),zIndex:1,filter:i?"drop-shadow(0 0 10px rgba(255, 107, 53, 0.1))":"none"},className:n().dynamic([["cdf0235daf430a20",[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t]]])+" "+`absolute inset-0 pointer-events-none ${e}`}),(0,r.jsx)(n(),{id:"cdf0235daf430a20",dynamic:[t,t,.5*t,.3*t,t,.7*t,.3*t,t,.2*t,-.1*t,.1*t,.2*t,-.1*t,.1*t],children:`@-webkit-keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-moz-keyframes subtle-grid-drift{0%{-moz-transform:translate(0,0);transform:translate(0,0)}100%{-moz-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-o-keyframes subtle-grid-drift{0%{-o-transform:translate(0,0);transform:translate(0,0)}100%{-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@keyframes subtle-grid-drift{0%{-webkit-transform:translate(0,0);-moz-transform:translate(0,0);-o-transform:translate(0,0);transform:translate(0,0)}100%{-webkit-transform:translate(${t}px,${t}px);-moz-transform:translate(${t}px,${t}px);-o-transform:translate(${t}px,${t}px);transform:translate(${t}px,${t}px)}}@-webkit-keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-moz-keyframes tech-grid-move{0%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-moz-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-o-keyframes tech-grid-move{0%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@keyframes tech-grid-move{0%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}25%{-webkit-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-moz-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);-o-transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg);transform:translate(${.5*t}px,${.3*t}px)rotate(.5deg)}50%{-webkit-transform:translate(${t}px,${.7*t}px)rotate(0deg);-moz-transform:translate(${t}px,${.7*t}px)rotate(0deg);-o-transform:translate(${t}px,${.7*t}px)rotate(0deg);transform:translate(${t}px,${.7*t}px)rotate(0deg)}75%{-webkit-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-moz-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);-o-transform:translate(${.3*t}px,${t}px)rotate(-.5deg);transform:translate(${.3*t}px,${t}px)rotate(-.5deg)}100%{-webkit-transform:translate(0,0)rotate(0deg);-moz-transform:translate(0,0)rotate(0deg);-o-transform:translate(0,0)rotate(0deg);transform:translate(0,0)rotate(0deg)}}@-webkit-keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-moz-keyframes premium-grid-float{0%,100%{-moz-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@-o-keyframes premium-grid-float{0%,100%{-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}@keyframes premium-grid-float{0%,100%{-webkit-transform:translate(0,0)scale(1);-moz-transform:translate(0,0)scale(1);-o-transform:translate(0,0)scale(1);transform:translate(0,0)scale(1)}25%{-webkit-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-moz-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);-o-transform:translate(${.2*t}px,${-.1*t}px)scale(1.01);transform:translate(${.2*t}px,${-.1*t}px)scale(1.01)}50%{-webkit-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-moz-transform:translate(${.1*t}px,${.2*t}px)scale(.99);-o-transform:translate(${.1*t}px,${.2*t}px)scale(.99);transform:translate(${.1*t}px,${.2*t}px)scale(.99)}75%{-webkit-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-moz-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);-o-transform:translate(${-.1*t}px,${.1*t}px)scale(1.005);transform:translate(${-.1*t}px,${.1*t}px)scale(1.005)}}`})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63319:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=a(65239),s=a(48088),n=a(88170),o=a.n(n),i=a(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let d={children:["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,94796)),"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signup\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\RoKey App\\rokey-app\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\RoKey App\\rokey-app\\src\\app\\auth\\signup\\page.tsx"],p={require:a,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},66524:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(43210);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},70440:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});var r=a(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94257:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});var r=a(43210);let s=r.forwardRef(function({title:e,titleId:t,...a},s){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},a),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},94735:e=>{"use strict";e.exports=require("events")},94796:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\auth\\\\signup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\RoKey App\\rokey-app\\src\\app\\auth\\signup\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[4447,221,1658,2535,7437],()=>a(63319));module.exports=r})();