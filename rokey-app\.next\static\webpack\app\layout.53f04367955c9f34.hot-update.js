"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"1ca66917750c\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjFjYTY2OTE3NzUwY1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DocumentTitleUpdater.tsx":
/*!*************************************************!*\
  !*** ./src/components/DocumentTitleUpdater.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DocumentTitleUpdater)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ var _s = $RefreshSig$();\n\n\nfunction DocumentTitleUpdater() {\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"DocumentTitleUpdater.useEffect\": ()=>{\n            if (typeof document !== 'undefined') {\n                // Simple title generation without using the problematic hook\n                const getPageTitle = {\n                    \"DocumentTitleUpdater.useEffect.getPageTitle\": (path)=>{\n                        switch(path){\n                            case '/dashboard':\n                                return 'Dashboard - RouKey';\n                            case '/playground':\n                                return 'Playground - RouKey';\n                            case '/my-models':\n                                return 'My Models - RouKey';\n                            case '/routing-setup':\n                                return 'Routing Setup - RouKey';\n                            case '/logs':\n                                return 'Logs - RouKey';\n                            case '/training':\n                                return 'Prompt Engineering - RouKey';\n                            case '/analytics':\n                                return 'Analytics - RouKey';\n                            case '/add-keys':\n                                return 'Add Keys - RouKey';\n                            case '/features':\n                                return 'Features - RouKey';\n                            case '/routing-strategies':\n                                return 'Routing Strategies - RouKey';\n                            case '/contact':\n                                return 'Contact - RouKey';\n                            case '/about':\n                                return 'About - RouKey';\n                            case '/pricing':\n                                return 'Pricing - RouKey';\n                            default:\n                                return 'RouKey - AI Gateway';\n                        }\n                    }\n                }[\"DocumentTitleUpdater.useEffect.getPageTitle\"];\n                document.title = getPageTitle(pathname);\n            }\n        }\n    }[\"DocumentTitleUpdater.useEffect\"], [\n        pathname\n    ]);\n    return null; // This component doesn't render anything\n}\n_s(DocumentTitleUpdater, \"V/ldUoOTYUs0Cb2F6bbxKSn7KxI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname\n    ];\n});\n_c = DocumentTitleUpdater;\nvar _c;\n$RefreshReg$(_c, \"DocumentTitleUpdater\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DocumentTitleUpdater.tsx\n"));

/***/ })

});