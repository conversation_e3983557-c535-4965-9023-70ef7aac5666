"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/routing-strategies/page",{

/***/ "(app-pages-browser)/./src/app/routing-strategies/page.tsx":
/*!*********************************************!*\
  !*** ./src/app/routing-strategies/page.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingStrategiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/landing/LandingNavbar */ \"(app-pages-browser)/./src/components/landing/LandingNavbar.tsx\");\n/* harmony import */ var _components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/landing/Footer */ \"(app-pages-browser)/./src/components/landing/Footer.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BeakerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,BeakerIcon,BoltIcon,CheckIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/InstantLink */ \"(app-pages-browser)/./src/components/ui/InstantLink.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst routingStrategies = [\n    {\n        id: 'none',\n        name: 'Default Load Balancing',\n        shortDescription: 'Automatic load balancing',\n        description: \"RouKey automatically load balances across all keys assigned to this configuration with intra-request retries. No extra setup needed.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        features: [\n            'Automatic load distribution',\n            'Built-in retry mechanisms',\n            'Zero configuration required',\n            'High availability'\n        ],\n        useCase: 'Perfect for simple setups where you want reliable distribution across multiple API keys without complex routing logic.',\n        performance: 'Excellent reliability with automatic failover',\n        complexity: 'Beginner',\n        color: 'from-gray-500 to-gray-600'\n    },\n    {\n        id: 'intelligent_role',\n        name: \"RouKey's Intelligent Role Routing\",\n        shortDescription: 'AI-powered role classification',\n        description: \"RouKey uses advanced AI to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.\",\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        features: [\n            \"RouKey's AI-powered classification\",\n            'Dynamic role assignment',\n            'Context-aware routing',\n            'Fallback to default model'\n        ],\n        useCase: 'Ideal for applications with diverse use cases like coding, writing, analysis, and general chat.',\n        performance: 'Superior accuracy with RouKey\\'s proprietary classification',\n        complexity: 'Intermediate',\n        color: 'from-[#ff6b35] to-[#f7931e]',\n        featured: true\n    },\n    {\n        id: 'complexity_round_robin',\n        name: \"RouKey's Complexity-Based Routing\",\n        shortDescription: 'Route by prompt complexity',\n        description: 'RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        features: [\n            'Intelligent complexity analysis',\n            'Optimized model selection',\n            'Cost-performance balance',\n            'Proximal level fallback'\n        ],\n        useCase: 'Perfect for cost optimization - route simple tasks to cheaper models and complex tasks to premium models.',\n        performance: 'Optimal cost-performance ratio',\n        complexity: 'Advanced',\n        color: 'from-blue-500 to-blue-600'\n    },\n    {\n        id: 'strict_fallback',\n        name: 'Strict Fallback Strategy',\n        shortDescription: 'Ordered failover sequence',\n        description: 'Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        features: [\n            'Predictable routing order',\n            'Guaranteed fallback chain',\n            'Manual priority control',\n            'Reliable failover'\n        ],\n        useCase: 'Best for scenarios where you have a preferred model hierarchy and want guaranteed fallback behavior.',\n        performance: 'Highly predictable with manual control',\n        complexity: 'Intermediate',\n        color: 'from-green-500 to-green-600'\n    },\n    {\n        id: 'cost_optimized',\n        name: \"RouKey's Smart Cost Optimization\",\n        shortDescription: 'Intelligent cost-performance balance',\n        description: 'RouKey intelligently balances cost and performance by routing simple tasks to cheaper models and complex tasks to premium models. Maximizes savings while ensuring quality.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        features: [\n            \"RouKey's learning algorithms\",\n            'Dynamic cost optimization',\n            'Quality preservation',\n            'Automatic model selection'\n        ],\n        useCase: 'Essential for production applications where cost control is critical but quality cannot be compromised.',\n        performance: 'Maximum cost savings with quality assurance',\n        complexity: 'Advanced',\n        color: 'from-emerald-500 to-emerald-600',\n        featured: true\n    },\n    {\n        id: 'ab_routing',\n        name: \"RouKey's A/B Testing Router\",\n        shortDescription: 'Continuous model optimization',\n        description: 'RouKey continuously tests different models with 15% of requests to find the best performing models for your specific use cases. Automatically optimizes routing based on quality and cost metrics.',\n        icon: _barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        features: [\n            'Continuous optimization',\n            'Data-driven decisions',\n            'Performance tracking',\n            'Automatic improvements'\n        ],\n        useCase: 'Perfect for applications that want to continuously improve performance and find the best models for their specific use cases.',\n        performance: 'Self-improving performance over time',\n        complexity: 'Advanced',\n        color: 'from-purple-500 to-purple-600'\n    }\n];\nfunction RoutingStrategiesPage() {\n    _s();\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(routingStrategies[1]); // Default to intelligent role\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_LandingNavbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-br from-gray-50 to-white border-b border-gray-200 pt-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.h1, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                className: \"text-4xl md:text-5xl font-bold text-black mb-6\",\n                                children: [\n                                    \"RouKey's Advanced \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-[#ff6b35]\",\n                                        children: \"Routing Strategies\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.1\n                                },\n                                className: \"text-xl text-gray-600 max-w-3xl mx-auto mb-8\",\n                                children: \"Choose the perfect routing strategy for your AI applications. From simple load balancing to advanced multi-role orchestration.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 20\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    href: \"/pricing\",\n                                    className: \"inline-flex items-center px-6 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors\",\n                                    children: [\n                                        \"View Pricing Plans\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: routingStrategies.map((strategy, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        onClick: ()=>setSelectedStrategy(strategy),\n                                        className: \"relative p-6 rounded-xl border-2 cursor-pointer transition-all duration-300 \".concat(selectedStrategy.id === strategy.id ? 'border-[#ff6b35] bg-[#ff6b35]/5' : 'border-gray-200 hover:border-gray-300'),\n                                        children: [\n                                            strategy.featured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute -top-3 -right-3 bg-[#ff6b35] text-white px-3 py-1 rounded-full text-sm font-semibold\",\n                                                children: \"Featured\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 rounded-lg bg-gradient-to-r \".concat(strategy.color),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(strategy.icon, {\n                                                            className: \"h-6 w-6 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 197,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-black mb-2\",\n                                                                children: strategy.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-600 text-sm mb-3\",\n                                                                children: strategy.shortDescription\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"px-2 py-1 rounded-full text-xs font-medium \".concat(strategy.complexity === 'Beginner' ? 'bg-green-100 text-green-800' : strategy.complexity === 'Intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'),\n                                                                        children: strategy.complexity\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    selectedStrategy.id === strategy.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                        className: \"h-5 w-5 text-[#ff6b35]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, strategy.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"sticky top-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    initial: {\n                                        opacity: 0,\n                                        x: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        x: 0\n                                    },\n                                    className: \"bg-white border border-gray-200 rounded-xl p-6 shadow-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-3 rounded-lg bg-gradient-to-r \".concat(selectedStrategy.color),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(selectedStrategy.icon, {\n                                                        className: \"h-6 w-6 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 233,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-xl font-bold text-black\",\n                                                            children: selectedStrategy.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-600\",\n                                                            children: selectedStrategy.shortDescription\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 237,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-700 mb-6\",\n                                            children: selectedStrategy.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Key Features\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"space-y-2\",\n                                                            children: selectedStrategy.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                            className: \"h-4 w-4 text-[#ff6b35]\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                            lineNumber: 249,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-700 text-sm\",\n                                                                            children: feature\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                            lineNumber: 250,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, index, true, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 25\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Best Use Case\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 257,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-sm\",\n                                                            children: selectedStrategy.useCase\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 258,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-semibold text-black mb-2\",\n                                                            children: \"Performance\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 262,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-700 text-sm\",\n                                                            children: selectedStrategy.performance\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-6 pt-6 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                href: \"/pricing\",\n                                                className: \"w-full inline-flex items-center justify-center px-4 py-3 bg-[#ff6b35] text-white font-semibold rounded-lg hover:bg-[#f7931e] transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"mr-2 h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Get Started\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, selectedStrategy.id, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-[#ff6b35] to-[#f7931e] py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: \"Ready to Optimize Your AI Routing?\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-white/90 mb-8\",\n                            children: \"Start with any strategy and switch anytime. No configuration required.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_InstantLink__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            href: \"/auth/signup?plan=professional\",\n                            className: \"inline-flex items-center px-8 py-4 bg-white text-[#ff6b35] font-bold rounded-xl hover:bg-gray-50 transition-colors text-lg\",\n                            children: [\n                                \"Start Building Now\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_BeakerIcon_BoltIcon_CheckIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"ml-3 h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                                    lineNumber: 296,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                    lineNumber: 284,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_landing_Footer__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-strategies\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingStrategiesPage, \"4ayf+QIiw8AtwaO/Cyi5ZAptEMo=\");\n_c = RoutingStrategiesPage;\nvar _c;\n$RefreshReg$(_c, \"RoutingStrategiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/routing-strategies/page.tsx\n"));

/***/ })

});